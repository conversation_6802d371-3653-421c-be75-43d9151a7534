# Telegram DM Bot

A console-based Telegram bot for sending direct messages to your contacts, groups, and channels.

## Features

- 🔐 Login with your Telegram account
- 📱 View all your contacts, groups, and channels
- 📤 Send messages to selected recipients
- 🎯 Select individual contacts or ranges
- ⚡ Rate limiting to avoid Telegram restrictions
- 🛡️ Error handling for privacy restrictions and flood limits
- 📊 Success/failure statistics

## Setup

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Get Telegram API credentials:**
   - Go to https://my.telegram.org/apps
   - Create a new application
   - Note down your `API ID` and `API Hash`

3. **Run the bot:**
   ```bash
   python telegram_dm_bot.py
   ```

4. **First time setup:**
   - Enter your API ID and API Hash when prompted
   - Complete phone number verification
   - The session will be saved for future use

## Usage

1. **Start the bot** - Run `python telegram_dm_bot.py`
2. **Select option 1** - Send messages to contacts
3. **Choose recipients:**
   - Enter numbers separated by commas: `1,3,5,10`
   - Enter a range: `1-20`
   - Enter `all` to select all contacts
4. **Compose your message** - Type your message and press Enter twice
5. **Confirm and send** - Review and confirm to send

## Important Notes

- ⚠️ **Rate Limiting**: The bot includes 1-second delays between messages to avoid Telegram's rate limits
- 🔒 **Privacy**: Some users may have privacy settings that prevent receiving messages
- 📵 **Flood Protection**: If you hit rate limits, the bot will wait and retry
- 💾 **Session Storage**: Your login session is saved locally in `telegram_dm_session.session`

## Safety Features

- Confirmation before sending messages
- Rate limiting to prevent account restrictions
- Error handling for various Telegram API errors
- Clear success/failure reporting

## Troubleshooting

- **"Invalid API credentials"**: Double-check your API ID and Hash from my.telegram.org
- **"Rate limited"**: Wait for the specified time before trying again
- **"Privacy restricted"**: The recipient has privacy settings preventing messages
- **Session issues**: Delete the `.session` file and login again

## Legal Notice

This tool is for personal use only. Respect Telegram's Terms of Service and don't use it for spam or harassment.
