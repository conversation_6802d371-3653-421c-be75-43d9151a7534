# Telegram DM Bot

A console-based Telegram bot for sending direct messages to your contacts, groups, and channels.

## Features

- 🔐 Login with your Telegram account
- 📱 View all your contacts, groups, and channels
- 📤 Send messages to selected recipients
- 👨‍👩‍👧‍👦 **BULK GROUP MESSAGING** - Send to 1000+ groups efficiently
- 🎯 Multiple selection options (individual, ranges, all, search)
- ⚡ Configurable rate limiting to avoid Telegram restrictions
- 🛡️ Advanced error handling for privacy restrictions and flood limits
- 📊 Detailed success/failure statistics and progress tracking
- 🔍 Search functionality for finding specific groups
- ⏱️ Customizable delays between messages

## Setup

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Get Telegram API credentials:**
   - Go to https://my.telegram.org/apps
   - Create a new application
   - Note down your `API ID` and `API Hash`

3. **Run the bot:**
   ```bash
   python telegram_dm_bot.py
   ```

4. **First time setup:**
   - Enter your API ID and API Hash when prompted
   - Complete phone number verification
   - The session will be saved for future use

## Usage

### Main Menu Options:
1. **📤 Send messages to selected contacts** - Choose specific users/groups
2. **👨‍👩‍👧‍👦 Bulk send to groups (FAST)** - Mass messaging to groups
3. **📊 View contact statistics** - See your contact counts
4. **🔄 Refresh contacts** - Reload contact list
5. **🚪 Exit** - Close application

### Bulk Group Messaging (Perfect for 1000+ groups):
1. **🎯 Send to ALL groups** - Message every group you're in
2. **📝 Select specific groups** - Choose individual groups
3. **📊 Send to groups by range** - Select groups 1-100, 101-200, etc.
4. **🔍 Search and select groups** - Find groups by name
5. **🔙 Back to main menu**

### Rate Limiting Options:
- **Fast (0.5s delay)** - Risk of rate limiting but faster
- **Normal (1s delay)** - Recommended balance
- **Safe (2s delay)** - Very safe, slower
- **Custom delay** - Set your own timing

### Recipient Selection:
- Enter numbers: `1,3,5,10`
- Enter range: `1-20`
- Enter `all` for all contacts
- Search by name for groups

## Important Notes

- ⚠️ **Rate Limiting**: Configurable delays (0.5s to custom) to avoid Telegram's rate limits
- 🔒 **Privacy**: Some users/groups may have privacy settings that prevent receiving messages
- 📵 **Flood Protection**: If you hit rate limits, the bot will automatically wait and retry
- 💾 **Session Storage**: Your login session is saved locally in `telegram_dm_session.session`
- 🚀 **Bulk Efficiency**: Optimized for sending to 1000+ groups with progress tracking
- 📊 **Statistics**: Real-time success/failure rates and error reporting

## Safety Features

- Confirmation before sending messages
- Rate limiting to prevent account restrictions
- Error handling for various Telegram API errors
- Clear success/failure reporting

## Troubleshooting

- **"Invalid API credentials"**: Double-check your API ID and Hash from my.telegram.org
- **"Rate limited"**: Wait for the specified time before trying again
- **"Privacy restricted"**: The recipient has privacy settings preventing messages
- **Session issues**: Delete the `.session` file and login again

## Legal Notice

This tool is for personal use only. Respect Telegram's Terms of Service and don't use it for spam or harassment.
