#!/usr/bin/env python3
"""
Test the asyncio event loop fix
"""
import asyncio
import threading
import time

def test_event_loop_fix():
    """Test the event loop approach used in the GUI"""
    print("🧪 Testing Asyncio Event Loop Fix")
    print("=" * 40)
    
    # Simulate the GUI approach
    event_loop = None
    loop_thread = None
    
    def start_event_loop_thread():
        """Start a dedicated event loop thread"""
        nonlocal event_loop
        def run_event_loop():
            event_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(event_loop)
            event_loop.run_forever()
        
        loop_thread = threading.Thread(target=run_event_loop, daemon=True)
        loop_thread.start()
        
        # Wait a moment for loop to start
        time.sleep(0.1)
        return event_loop
    
    async def test_async_function():
        """Test async function"""
        print("✅ Async function called successfully")
        await asyncio.sleep(0.1)
        return "Success!"
    
    def test_from_thread():
        """Test calling async function from thread"""
        try:
            future = asyncio.run_coroutine_threadsafe(test_async_function(), event_loop)
            result = future.result(timeout=5)
            print(f"✅ Result from thread: {result}")
            return True
        except Exception as e:
            print(f"❌ Error from thread: {e}")
            return False
    
    # Start the event loop
    print("1. Starting dedicated event loop thread...")
    event_loop = start_event_loop_thread()
    
    if event_loop:
        print("✅ Event loop started successfully")
    else:
        print("❌ Failed to start event loop")
        return False
    
    # Test calling async function from main thread
    print("\n2. Testing async call from main thread...")
    success = test_from_thread()
    
    # Test calling from another thread
    print("\n3. Testing async call from another thread...")
    def thread_test():
        return test_from_thread()
    
    test_thread = threading.Thread(target=thread_test)
    test_thread.start()
    test_thread.join()
    
    # Cleanup
    print("\n4. Cleaning up...")
    if event_loop and not event_loop.is_closed():
        event_loop.call_soon_threadsafe(event_loop.stop)
    
    print("\n🎉 Asyncio event loop fix test completed!")
    print("This approach should resolve the 'event loop not change after connection' error.")
    
    return success

if __name__ == "__main__":
    test_event_loop_fix()
