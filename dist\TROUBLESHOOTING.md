# 🔧 Telegram DM Bot - Troubleshooting Guide

## Common Issues and Solutions

### ❌ "Not Connecting" Error

**Possible Causes:**
1. **No Internet Connection**
2. **Firewall/Antivirus Blocking**
3. **Invalid API Credentials**
4. **Telegram Servers Down**

**Solutions:**
1. **Test Your Connection:**
   - Click "Test Connection" button in the app
   - Check if internet is working in browser
   - Try visiting https://web.telegram.org

2. **Check Firewall/Antivirus:**
   - Temporarily disable antivirus
   - Add TelegramDMBot.exe to firewall exceptions
   - Try running as administrator

3. **Verify API Credentials:**
   - API ID should be a number (e.g., 23806500)
   - API Hash should be 32 characters long
   - Get new credentials from https://my.telegram.org/apps

### 📱 "Verification Code Not Coming" Error

**Possible Causes:**
1. **Wrong Phone Number Format**
2. **Phone Number Not Registered with Telegram**
3. **SMS Delivery Issues**
4. **Rate Limiting**

**Solutions:**
1. **Check Phone Number Format:**
   ```
   ✅ Correct: +**********
   ❌ Wrong: **********
   ❌ Wrong: ******-567-890
   ❌ Wrong: ****** 567 890
   ```

2. **Verify Phone Number:**
   - Must be registered with Telegram
   - Try logging into Telegram app with same number
   - Check if number is banned

3. **Check SMS/Telegram App:**
   - Code might come via Telegram app instead of SMS
   - Check spam/blocked messages
   - Wait 2-3 minutes for delivery

4. **Rate Limiting:**
   - Wait 5-10 minutes between attempts
   - Don't request codes too frequently

### 🌐 Network Issues

**Corporate/School Networks:**
- Telegram might be blocked
- Try mobile hotspot
- Use VPN if allowed

**Home Networks:**
- Restart router
- Check if Telegram is blocked by ISP
- Try different DNS (8.8.8.8, 1.1.1.1)

### 🔐 API Credential Issues

**Getting New Credentials:**
1. Go to https://my.telegram.org/apps
2. Login with your Telegram account
3. Create new application:
   - App title: Any name
   - Short name: 5-32 characters
   - Platform: Desktop
4. Copy API ID and API Hash

**Common Mistakes:**
- Using Bot Token instead of API credentials
- Copying incomplete API Hash
- Using someone else's credentials

### 📞 Phone Number Issues

**Supported Formats:**
```
+**********     (US)
+************   (UK)
+***********    (France)
+***********    (Germany)
+***********    (India)
```

**Not Supported:**
- Numbers without country code
- Numbers with spaces or dashes
- Landline numbers (mobile only)

### 🚫 Account Issues

**Phone Number Banned:**
- Use different phone number
- Contact Telegram support
- Wait for ban to expire

**Account Restricted:**
- Check Telegram app for restrictions
- Follow Telegram's terms of service
- Wait for restrictions to lift

## Step-by-Step Debugging

### 1. Test Connection
```
Click "Test Connection" → Check results in Logs tab
```

### 2. Check Logs
```
Go to "Logs" tab → Look for error messages
```

### 3. Try Different Phone Format
```
+[country code][phone number]
Example: +**********
```

### 4. Wait and Retry
```
Wait 5 minutes → Try again
```

### 5. Restart Application
```
Close app → Restart → Try again
```

## Getting Help

### Information to Provide:
1. **Error message** from Logs tab
2. **Phone number format** you're using (hide actual digits)
3. **Country** you're in
4. **Network type** (home/work/mobile)

### What NOT to Share:
- Your actual phone number
- API Hash (keep it secret)
- Verification codes

## Quick Fixes

### Connection Issues:
1. Run as administrator
2. Disable antivirus temporarily
3. Try mobile hotspot
4. Restart computer

### Code Issues:
1. Check Telegram app for code
2. Wait 2-3 minutes
3. Try different phone format
4. Request new code after 5 minutes

### App Issues:
1. Restart application
2. Delete session file
3. Rebuild from source
4. Try on different computer

## Still Having Issues?

If none of these solutions work:
1. Check the Logs tab for specific error messages
2. Try the console version first
3. Test with a different phone number
4. Contact Telegram support for account issues

Remember: The app needs a working internet connection and valid Telegram account to function!
