"""
Configuration file for Telegram DM Bot
"""
import os
from typing import Optional

class Config:
    """Configuration class for the Telegram bot"""
    
    # Telegram API credentials - Get these from https://my.telegram.org/apps
    API_ID: Optional[int] = None
    API_HASH: Optional[str] = None
    
    # Session file name
    SESSION_NAME = "telegram_dm_bot"
    
    # Rate limiting settings (messages per minute)
    RATE_LIMIT = 20
    
    # Maximum message length
    MAX_MESSAGE_LENGTH = 4096
    
    @classmethod
    def load_from_env(cls):
        """Load configuration from environment variables"""
        cls.API_ID = os.getenv('TELEGRAM_API_ID')
        cls.API_HASH = os.getenv('TELEGRAM_API_HASH')
        
        if cls.API_ID:
            cls.API_ID = int(cls.API_ID)
    
    @classmethod
    def is_configured(cls) -> bool:
        """Check if the configuration is complete"""
        return cls.API_ID is not None and cls.API_HASH is not None
    
    @classmethod
    def set_credentials(cls, api_id: int, api_hash: str):
        """Set API credentials"""
        cls.API_ID = api_id
        cls.API_HASH = api_hash

# Load configuration from environment on import
Config.load_from_env()
