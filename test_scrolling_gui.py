#!/usr/bin/env python3
"""
Test the scrolling GUI layout
"""
import tkinter as tk
from tkinter import ttk

def test_scrolling_layout():
    """Test the scrolling layout similar to the main app"""
    root = tk.Tk()
    root.title("Test Scrolling Layout")
    root.geometry("900x700")
    
    # Create main canvas and scrollbar for scrolling
    canvas = tk.Canvas(root, bg='#f0f0f0')
    scrollbar = ttk.Scrollbar(root, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    # Pack canvas and scrollbar
    canvas.pack(side="left", fill="both", expand=True, padx=10, pady=10)
    scrollbar.pack(side="right", fill="y")
    
    # Bind mousewheel to canvas
    def _on_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    # Add test content
    title_label = tk.Label(scrollable_frame, text="🚀 TELEGRAM DM BOT", 
                          font=("Arial", 20, "bold"))
    title_label.pack(pady=10)
    
    # Connection status
    conn_frame = ttk.LabelFrame(scrollable_frame, text="Connection Status", padding=10)
    conn_frame.pack(fill=tk.X, pady=10)
    
    status_label = tk.Label(conn_frame, text="✅ Connected", fg='green')
    status_label.pack(side=tk.LEFT)
    
    # Login container
    login_container = ttk.LabelFrame(scrollable_frame, text="🔐 Login to Telegram", padding=15)
    login_container.pack(fill=tk.X, pady=10)
    
    # Step 1: Phone
    phone_frame = ttk.Frame(login_container)
    phone_frame.pack(fill=tk.X, pady=5)
    
    tk.Label(phone_frame, text="📱 Step 1: Enter Phone Number", 
            font=("Arial", 11, "bold")).pack(anchor=tk.W)
    tk.Label(phone_frame, text="Examples: ****** 863 3617 or +1234567890", 
            font=("Arial", 9), fg='gray').pack(anchor=tk.W, pady=(0,5))
    
    phone_input_frame = tk.Frame(phone_frame)
    phone_input_frame.pack(fill=tk.X, pady=5)
    
    phone_entry = tk.Entry(phone_input_frame, font=("Arial", 11), width=20)
    phone_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
    phone_entry.insert(0, "****** 863 3617")
    
    send_code_btn = tk.Button(phone_input_frame, text="Code Sent ✅", 
                             bg='#2196F3', fg='white', font=("Arial", 10))
    send_code_btn.pack(side=tk.RIGHT, padx=(10,0))
    
    # Step 2: Code
    code_frame = ttk.Frame(login_container)
    code_frame.pack(fill=tk.X, pady=5)
    
    tk.Label(code_frame, text="📨 Step 2: Enter Verification Code", 
            font=("Arial", 11, "bold")).pack(anchor=tk.W, pady=(10,0))
    code_status_label = tk.Label(code_frame, text="✅ Code sent to your phone", 
                                fg='green', font=("Arial", 9))
    code_status_label.pack(anchor=tk.W, pady=(0,5))
    
    code_input_frame = tk.Frame(code_frame)
    code_input_frame.pack(fill=tk.X, pady=5)
    
    code_entry = tk.Entry(code_input_frame, font=("Arial", 11), width=15)
    code_entry.pack(side=tk.LEFT)
    code_entry.insert(0, "12345")
    
    login_btn = tk.Button(code_input_frame, text="2FA Required", 
                         bg='#4CAF50', fg='white', font=("Arial", 10))
    login_btn.pack(side=tk.RIGHT, padx=(10,0))
    
    # Step 3: Password
    password_frame = ttk.Frame(login_container)
    password_frame.pack(fill=tk.X, pady=5)
    
    tk.Label(password_frame, text="🔐 Step 3: Two-Step Verification", 
            font=("Arial", 11, "bold")).pack(anchor=tk.W, pady=(10,0))
    password_status_label = tk.Label(password_frame, text="🔒 2FA password required", 
                                    fg='orange', font=("Arial", 9))
    password_status_label.pack(anchor=tk.W, pady=(0,5))
    
    password_input_frame = tk.Frame(password_frame)
    password_input_frame.pack(fill=tk.X, pady=5)
    
    password_entry = tk.Entry(password_input_frame, font=("Arial", 11), width=20, show="*")
    password_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
    password_entry.insert(0, "password123")
    
    password_btn = tk.Button(password_input_frame, text="Submit Password", 
                            bg='#FF5722', fg='white', font=("Arial", 10))
    password_btn.pack(side=tk.RIGHT, padx=(10,0))
    
    # Add some extra content to test scrolling
    for i in range(5):
        test_frame = ttk.LabelFrame(scrollable_frame, text=f"Test Section {i+1}", padding=10)
        test_frame.pack(fill=tk.X, pady=5)
        tk.Label(test_frame, text=f"This is test content {i+1} to demonstrate scrolling").pack()
    
    # Update scroll region
    def update_scroll_region():
        canvas.update_idletasks()
        canvas.configure(scrollregion=canvas.bbox("all"))
    
    # Test scroll to bottom button
    def scroll_to_bottom():
        canvas.update_idletasks()
        canvas.yview_moveto(1.0)
    
    test_btn = tk.Button(scrollable_frame, text="Scroll to Bottom (Test 2FA Visibility)", 
                        command=scroll_to_bottom, bg='#9C27B0', fg='white')
    test_btn.pack(pady=10)
    
    # Initial scroll region update
    root.after(100, update_scroll_region)
    
    print("🧪 Testing Scrolling Layout:")
    print("- Use mouse wheel to scroll")
    print("- Click 'Scroll to Bottom' to test 2FA visibility")
    print("- All login steps should be visible with scrolling")
    
    root.mainloop()

if __name__ == "__main__":
    test_scrolling_layout()
