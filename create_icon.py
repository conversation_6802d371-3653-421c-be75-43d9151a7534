#!/usr/bin/env python3
"""
Create a simple icon for the Windows app
"""
try:
    from PIL import Image, ImageDraw, ImageFont
    
    def create_icon():
        # Create a 256x256 image with blue background
        size = 256
        img = Image.new('RGB', (size, size), color='#1DA1F2')  # Twitter blue
        draw = ImageDraw.Draw(img)
        
        # Draw a simple telegram-like icon
        # Draw a circle
        circle_size = 180
        circle_pos = (size - circle_size) // 2
        draw.ellipse([circle_pos, circle_pos, circle_pos + circle_size, circle_pos + circle_size], 
                    fill='white', outline='#1DA1F2', width=4)
        
        # Draw "TG" text
        try:
            font = ImageFont.truetype("arial.ttf", 80)
        except:
            font = ImageFont.load_default()
        
        text = "TG"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        text_x = (size - text_width) // 2
        text_y = (size - text_height) // 2 - 10
        
        draw.text((text_x, text_y), text, fill='#1DA1F2', font=font)
        
        # Save as ICO file
        img.save('icon.ico', format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
        print("✅ Icon created: icon.ico")
        
    create_icon()
    
except ImportError:
    print("⚠️  PIL not available, creating simple text icon...")
    # Create a simple text-based icon placeholder
    with open('icon.txt', 'w') as f:
        f.write("Icon placeholder - install Pillow for real icon")
    print("✅ Icon placeholder created")
