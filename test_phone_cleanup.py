#!/usr/bin/env python3
"""
Test phone number cleanup function
"""

def clean_phone_number(phone_raw):
    """Clean up phone number - remove spaces, dashes, parentheses"""
    phone = phone_raw.replace(' ', '').replace('-', '').replace('(', '').replace(')', '').replace('.', '')
    return phone

# Test cases
test_numbers = [
    "****** 863 3617",
    "******-863-3617", 
    "+1(219)863-3617",
    "******.863.3617",
    "+44 7123 456 789",
    "+33 1 23 45 67 89",
    "+1234567890"
]

print("🧪 Testing Phone Number Cleanup:")
print("=" * 50)

for original in test_numbers:
    cleaned = clean_phone_number(original)
    digits = cleaned[1:] if cleaned.startswith('+') else cleaned
    is_valid = cleaned.startswith('+') and digits.isdigit() and 7 <= len(digits) <= 15
    
    print(f"Original: {original}")
    print(f"Cleaned:  {cleaned}")
    print(f"Valid:    {'✅ YES' if is_valid else '❌ NO'}")
    print("-" * 30)

print("\n✅ Your number '****** 863 3617' will be cleaned to '+12198633617'")
print("This should now work with the updated app!")
