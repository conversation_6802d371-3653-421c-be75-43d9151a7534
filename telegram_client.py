"""
Telegram client wrapper for DM bot
"""
import asyncio
from typing import List, Dict, Optional, Union
from telethon import TelegramClient, events
from telethon.tl.types import User, Chat, Channel, PeerUser, PeerChat, PeerChannel
from telethon.errors import FloodWaitError, PeerFloodError, UserPrivacyRestrictedError
from asyncio_throttle import Throttler
import time
from rich.console import Console
from rich.progress import Progress, TaskID

from config import Config

console = Console()

class TelegramDMClient:
    """Telegram client for sending direct messages"""
    
    def __init__(self):
        self.client: Optional[TelegramClient] = None
        self.throttler = Throttler(rate_limit=Config.RATE_LIMIT, period=60)
        self.is_connected = False
        
    async def initialize(self) -> bool:
        """Initialize and connect the Telegram client"""
        try:
            if not Config.is_configured():
                console.print("[red]❌ Telegram API credentials not configured![/red]")
                return False
                
            self.client = TelegramClient(
                Config.SESSION_NAME,
                Config.API_ID,
                Config.API_HASH
            )
            
            await self.client.start()
            self.is_connected = True
            
            # Get current user info
            me = await self.client.get_me()
            console.print(f"[green]✅ Successfully logged in as: {me.first_name} (@{me.username})[/green]")
            
            return True
            
        except Exception as e:
            console.print(f"[red]❌ Failed to initialize Telegram client: {str(e)}[/red]")
            return False
    
    async def get_dialogs(self) -> List[Dict]:
        """Get all dialogs (chats, groups, channels)"""
        if not self.is_connected:
            return []
            
        try:
            dialogs = []
            async for dialog in self.client.iter_dialogs():
                entity_type = "Unknown"
                if isinstance(dialog.entity, User):
                    entity_type = "User"
                elif isinstance(dialog.entity, Chat):
                    entity_type = "Group"
                elif isinstance(dialog.entity, Channel):
                    entity_type = "Channel" if dialog.entity.broadcast else "Supergroup"
                
                dialogs.append({
                    'id': dialog.entity.id,
                    'name': dialog.name,
                    'type': entity_type,
                    'entity': dialog.entity,
                    'unread_count': dialog.unread_count
                })
            
            return dialogs
            
        except Exception as e:
            console.print(f"[red]❌ Failed to get dialogs: {str(e)}[/red]")
            return []
    
    async def send_message(self, entity_id: int, message: str) -> Dict:
        """Send a message to a specific entity"""
        if not self.is_connected:
            return {'success': False, 'error': 'Client not connected'}
        
        try:
            # Rate limiting
            async with self.throttler:
                await self.client.send_message(entity_id, message)
                return {'success': True, 'error': None}
                
        except FloodWaitError as e:
            error_msg = f"Rate limited. Wait {e.seconds} seconds"
            return {'success': False, 'error': error_msg}
            
        except PeerFloodError:
            error_msg = "Too many requests. Try again later"
            return {'success': False, 'error': error_msg}
            
        except UserPrivacyRestrictedError:
            error_msg = "User privacy settings prevent messaging"
            return {'success': False, 'error': error_msg}
            
        except Exception as e:
            error_msg = f"Failed to send message: {str(e)}"
            return {'success': False, 'error': error_msg}
    
    async def send_bulk_messages(self, recipients: List[int], message: str) -> Dict:
        """Send messages to multiple recipients with progress tracking"""
        if not self.is_connected:
            return {'success': False, 'error': 'Client not connected'}
        
        results = {
            'total': len(recipients),
            'successful': 0,
            'failed': 0,
            'errors': []
        }
        
        with Progress() as progress:
            task = progress.add_task("[cyan]Sending messages...", total=len(recipients))
            
            for recipient_id in recipients:
                result = await self.send_message(recipient_id, message)
                
                if result['success']:
                    results['successful'] += 1
                else:
                    results['failed'] += 1
                    results['errors'].append({
                        'recipient_id': recipient_id,
                        'error': result['error']
                    })
                
                progress.update(task, advance=1)
                
                # Small delay between messages
                await asyncio.sleep(0.5)
        
        return results
    
    async def disconnect(self):
        """Disconnect the client"""
        if self.client and self.is_connected:
            await self.client.disconnect()
            self.is_connected = False
            console.print("[yellow]📱 Disconnected from Telegram[/yellow]")
