#!/usr/bin/env python3
"""
Build script to create Windows executable for Telegram DM Bot
"""
import os
import subprocess
import sys

def install_requirements():
    """Install required packages for building"""
    print("📦 Installing build requirements...")

    packages = [
        "pyinstaller",
        "telethon",
        "auto-py-to-exe"  # Optional GUI for PyInstaller
    ]

    for package in packages:
        try:
            print(f"Installing {package}...")
            subprocess.run([sys.executable, "-m", "pip", "install", package], check=True)
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")
            return False

    return True

def create_spec_file():
    """Create PyInstaller spec file for better control"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['telegram_dm_bot.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'telethon',
        'telethon.tl',
        'telethon.tl.types',
        'telethon.errors',
        'asyncio',
        'webbrowser',
        'time'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='TelegramDMBot',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico'  # Optional: add icon file
)
'''

    with open('telegram_dm_bot.spec', 'w') as f:
        f.write(spec_content)

    print("✅ Spec file created")

def build_executable():
    """Build the Windows executable"""
    print("🔨 Building Windows executable...")

    try:
        # Create spec file first
        create_spec_file()

        # Build using spec file
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--console",
            "--name", "TelegramDMBot",
            "--distpath", "dist",
            "--workpath", "build",
            "telegram_dm_bot.py"
        ]

        subprocess.run(cmd, check=True)
        print("✅ Build completed successfully!")
        print("📁 Executable created in 'dist' folder")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        return False

def create_batch_file():
    """Create a batch file for easy running"""
    batch_content = '''@echo off
title Telegram DM Bot - Bulk Messaging Tool
echo Starting Telegram DM Bot...
echo.
TelegramDMBot.exe
pause
'''

    with open('dist/run_telegram_bot.bat', 'w') as f:
        f.write(batch_content)

    print("✅ Batch file created for easy running")

def main():
    """Main build process"""
    print("🚀 TELEGRAM DM BOT - WINDOWS BUILD TOOL")
    print("=" * 50)

    # Check if we're in the right directory
    if not os.path.exists('telegram_dm_bot.py'):
        print("❌ telegram_dm_bot.py not found!")
        print("Please run this script in the same directory as telegram_dm_bot.py")
        return

    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements")
        return

    # Build executable
    if build_executable():
        create_batch_file()

        print("\n" + "=" * 50)
        print("🎉 BUILD COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        print("📁 Your Windows app is ready in the 'dist' folder:")
        print("   • TelegramDMBot.exe - Main executable")
        print("   • run_telegram_bot.bat - Easy launcher")
        print("\n🚀 To run your app:")
        print("   1. Go to the 'dist' folder")
        print("   2. Double-click 'TelegramDMBot.exe' or 'run_telegram_bot.bat'")
        print("   3. Follow the on-screen instructions")
        print("\n📦 You can distribute the entire 'dist' folder to other computers!")
    else:
        print("❌ Build failed!")

if __name__ == "__main__":
    main()