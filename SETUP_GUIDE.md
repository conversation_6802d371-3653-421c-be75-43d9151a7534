# 🚀 Telegram DM Bot - Complete Setup Guide

## Quick Start (2 Minutes)

### Step 1: Install Dependencies
```bash
pip install telethon
```

### Step 2: Run the Bot
```bash
python telegram_dm_bot.py
```

### Step 3: Follow the Guided Setup
The bot will automatically:
1. Open https://my.telegram.org/apps in your browser
2. Guide you through creating a Telegram application
3. Help you get your API credentials
4. Save everything for future use

## Detailed Setup Process

### 1. Create Telegram Application

When you run the bot, it will:
- Open the Telegram API page automatically
- Guide you through login with your phone number
- Help you fill out the application form

**Application Form Fields:**
- **App title**: Any name (e.g., "My DM Bot")
- **Short name**: 5-32 alphanumeric characters (e.g., "mydmbot")
- **URL**: Leave empty (optional)
- **Platform**: Select "Desktop"
- **Description**: Any description (e.g., "Bulk messaging bot")

### 2. Get API Credentials

After creating the app, you'll see:
- **API ID**: A number (e.g., 1234567)
- **API Hash**: A long string (e.g., "abcd1234...")

The bot will ask you to copy and paste these values.

### 3. Login to Telegram

The bot will:
- Ask for your phone number
- Send you a verification code
- Save your session for future use

### 4. Start Messaging!

Once setup is complete, you can:
- Send messages to selected contacts
- **Bulk message 1000+ groups**
- View contact statistics
- Search and filter groups

## Troubleshooting

### "Invalid API credentials"
- Make sure you copied the API ID and Hash correctly
- API ID should be numbers only
- API Hash should be the full string

### "Phone number invalid"
- Include country code (e.g., +**********)
- Use the same number as your Telegram account

### "Session expired"
- Delete the `.session` file and run the bot again
- You'll need to re-enter your phone number and verification code

### Browser doesn't open
- The bot will show the URL to visit manually
- Go to https://my.telegram.org/apps in any browser

## Security Notes

- Your API credentials are stored locally in the session file
- Never share your API Hash with anyone
- The bot only accesses your own Telegram account
- All data stays on your computer

## Features After Setup

### Bulk Group Messaging
- Send to ALL groups at once
- Select specific groups by number
- Send by range (1-100, 101-200, etc.)
- Search groups by name

### Rate Limiting Options
- Fast (0.5s delay) - 7200 groups/hour
- Normal (1s delay) - 3600 groups/hour
- Safe (2s delay) - 1800 groups/hour
- Custom timing

### Safety Features
- Automatic retry on rate limits
- Detailed error reporting
- Progress tracking
- Confirmation prompts for bulk operations

## Ready to Start?

Just run:
```bash
python telegram_dm_bot.py
```

The bot will guide you through everything! 🚀
