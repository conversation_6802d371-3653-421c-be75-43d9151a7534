
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), netrc (delayed, conditional), getpass (delayed, optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional)
missing module named 'collections.abc' - imported by traceback (top-level), typing (top-level), inspect (top-level), logging (top-level), importlib.resources.readers (top-level), selectors (top-level), tracemalloc (top-level), asyncio.base_events (top-level), http.client (top-level), asyncio.coroutines (top-level), socks (optional), sqlite3.dbapi2 (top-level), multidict._abc (top-level), multidict._multidict_py (top-level), typing_extensions (top-level), multidict (conditional), attr._compat (top-level), attr._make (top-level), yarl._query (top-level), yarl._url (top-level), propcache._helpers_py (top-level), yarl._path (top-level), aiohttp.web (top-level), aiohttp.abc (top-level), frozenlist (top-level), aiohttp.client_reqrep (top-level), aiohttp.multipart (top-level), aiohttp.compression_utils (conditional), aiohttp.payload (top-level), aiohttp.connector (conditional), aiohttp.web_response (top-level), aiohttp.client_middlewares (top-level), aiohttp.cookiejar (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), _pyrepl.pager (delayed, optional), getpass (optional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named posix - imported by posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional)
missing module named resource - imported by posix (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named collections.Callable - imported by collections (optional), socks (optional)
missing module named telethon.tl.types.TypeX - imported by telethon.tl.types (conditional), telethon.tl.functions (conditional)
missing module named telethon.tl.types.TypeType - imported by telethon.tl.types (conditional), telethon.tl.functions (conditional)
missing module named 'hachoir.parser' - imported by telethon.utils (optional)
missing module named 'hachoir.metadata' - imported by telethon.utils (optional)
missing module named hachoir - imported by telethon.utils (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), rlcompleter (optional), pdb (delayed, optional), sqlite3.__main__ (delayed, conditional, optional)
missing module named cryptg - imported by telethon.crypto.aes (optional)
missing module named 'python_socks.async_' - imported by telethon.network.connection.connection (delayed, conditional)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named python_socks - imported by telethon.network.connection.connection (delayed, conditional, optional)
missing module named uvloop - imported by aiohttp.worker (delayed)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by attr._compat (conditional)
missing module named async_timeout - imported by aiohttp.helpers (conditional), aiohttp.web_ws (conditional), aiohttp.client_ws (conditional)
missing module named 'gunicorn.workers' - imported by aiohttp.worker (top-level)
missing module named gunicorn - imported by aiohttp.worker (top-level)
missing module named aiodns - imported by aiohttp.resolver (optional)
missing module named brotli - imported by aiohttp.compression_utils (optional)
missing module named brotlicffi - imported by aiohttp.compression_utils (optional)
missing module named 'PIL.Image' - imported by telethon.client.uploads (optional)
missing module named PIL - imported by telethon.client.uploads (optional)
missing module named telethon.__name__ - imported by telethon (top-level), telethon.client.telegrambaseclient (top-level)
