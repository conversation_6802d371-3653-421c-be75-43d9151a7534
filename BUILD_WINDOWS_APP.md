# 🚀 Build Windows Desktop App

Convert your Telegram DM Bot into a standalone Windows executable that runs on any Windows computer without requiring Python installation.

## Quick Build Process

### Step 1: Install Build Tools
```bash
pip install -r requirements.txt
```

### Step 2: Run Build Script
```bash
python build_windows_app.py
```

### Step 3: Get Your App
Your Windows app will be created in the `dist` folder:
- `TelegramDMBot.exe` - Main executable
- `run_telegram_bot.bat` - Easy launcher

## What You Get

### 📦 Standalone Executable
- **No Python required** on target computers
- **Single file** - easy to distribute
- **All dependencies included** - telethon, asyncio, etc.
- **Windows optimized** - clear screen, proper exit handling

### 🎯 Features Included
- ✅ Bulk messaging to 1000+ groups
- ✅ API credentials pre-configured (PumpX Bot)
- ✅ Rate limiting options (0.5s to custom)
- ✅ Progress tracking and error handling
- ✅ Group search and selection
- ✅ Windows-friendly interface

### 📁 Distribution
You can copy the entire `dist` folder to any Windows computer and it will work immediately!

## Manual Build (Alternative)

If the automated script doesn't work, you can build manually:

```bash
# Install PyInstaller
pip install pyinstaller telethon

# Create executable
pyinstaller --onefile --console --name TelegramDMBot telegram_dm_bot.py

# Your app will be in dist/TelegramDMBot.exe
```

## Advanced Build Options

### With Icon
```bash
python create_icon.py  # Creates icon.ico
pyinstaller --onefile --console --icon=icon.ico --name TelegramDMBot telegram_dm_bot.py
```

### Smaller File Size
```bash
pyinstaller --onefile --console --exclude-module matplotlib --exclude-module numpy --name TelegramDMBot telegram_dm_bot.py
```

### Hidden Console (GUI-like)
```bash
pyinstaller --onefile --noconsole --name TelegramDMBot telegram_dm_bot.py
```

## Troubleshooting

### Build Fails
- Make sure you have Python 3.7+ installed
- Install Visual C++ Build Tools if on Windows
- Try: `pip install --upgrade pyinstaller`

### Large File Size
- Normal size is 15-30MB (includes Python runtime)
- Use UPX compression: `pip install upx-ucl`
- Add `--upx-dir=path/to/upx` to pyinstaller command

### Missing Modules
- Add to spec file: `hiddenimports=['missing_module']`
- Or use: `pyinstaller --hidden-import=missing_module`

### Antivirus Issues
- Some antivirus software flags PyInstaller executables
- Add exception for your build folder
- Use code signing certificate for distribution

## Distribution Tips

### For Single Computer
- Just copy `TelegramDMBot.exe`
- Include `run_telegram_bot.bat` for easier launching

### For Multiple Computers
- Zip the entire `dist` folder
- Include README with usage instructions
- Test on clean Windows machine first

### Professional Distribution
- Add code signing certificate
- Create installer with NSIS or Inno Setup
- Include uninstaller and desktop shortcuts

## File Structure After Build

```
dist/
├── TelegramDMBot.exe          # Main executable
├── run_telegram_bot.bat       # Easy launcher
└── telegram_dm_session.session # Created after first login
```

## Usage After Build

1. **Double-click** `TelegramDMBot.exe` or `run_telegram_bot.bat`
2. **Enter phone number** when prompted
3. **Enter verification code** from Telegram
4. **Start bulk messaging** to your groups!

The app will remember your login for future use.

## Security Notes

- API credentials are compiled into the executable
- Session files are created locally
- No data is sent to external servers
- All communication is directly with Telegram

## Performance

- **Startup time**: 2-5 seconds
- **Memory usage**: 50-100MB
- **Group messaging**: Same speed as Python version
- **File size**: 15-30MB (standalone)

Ready to build your Windows app? Run `python build_windows_app.py`! 🚀
