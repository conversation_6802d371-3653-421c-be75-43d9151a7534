#!/usr/bin/env python3
"""
Test script to verify the Telegram bot works
"""
import sys

def test_imports():
    """Test if all required modules can be imported"""
    try:
        import asyncio
        print("✅ asyncio imported successfully")
        
        import telethon
        print(f"✅ telethon imported successfully (version: {telethon.__version__})")
        
        from telegram_dm_bot import TelegramDMBot
        print("✅ TelegramDMBot imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def main():
    print("🧪 Testing Telegram DM Bot...")
    print("=" * 40)
    
    if test_imports():
        print("\n✅ All tests passed!")
        print("\n🚀 You can now run the bot with:")
        print("   python telegram_dm_bot.py")
        print("\n📝 Make sure to have your Telegram API credentials ready:")
        print("   - API ID")
        print("   - API Hash")
        print("   Get them from: https://my.telegram.org/apps")
    else:
        print("\n❌ Tests failed!")
        print("Please install the required dependencies:")
        print("   pip install telethon")

if __name__ == "__main__":
    main()
