#!/usr/bin/env python3
"""
Telegram DM Bot - Send messages to users/groups via console interface
"""
import asyncio
import os
import sys
from typing import List, Dict, Optional
from telethon import TelegramClient
from telethon.tl.types import User, <PERSON>t, Channel
from telethon.errors import FloodWaitError, PeerFloodError, UserPrivacyRestrictedError
import time

class TelegramDMBot:
    def __init__(self):
        self.client = None
        self.api_id = None
        self.api_hash = None
        self.session_name = "telegram_dm_session"
        
    def setup_credentials(self):
        """Setup Telegram API credentials"""
        print("🔧 Setting up Telegram API credentials...")
        print("📝 Get your API credentials from: https://my.telegram.org/apps")
        print()
        
        while not self.api_id:
            try:
                api_id_input = input("Enter your API ID: ").strip()
                self.api_id = int(api_id_input)
            except ValueError:
                print("❌ Invalid API ID. Please enter a number.")
        
        while not self.api_hash:
            self.api_hash = input("Enter your API Hash: ").strip()
            if not self.api_hash:
                print("❌ API Hash cannot be empty.")
        
        print("✅ Credentials configured!")
        print()
    
    async def connect(self):
        """Connect to Telegram"""
        if not self.api_id or not self.api_hash:
            self.setup_credentials()
        
        try:
            self.client = TelegramClient(self.session_name, self.api_id, self.api_hash)
            print("🔄 Connecting to Telegram...")
            await self.client.start()
            
            me = await self.client.get_me()
            print(f"✅ Successfully logged in as: {me.first_name}")
            if me.username:
                print(f"   Username: @{me.username}")
            print()
            return True
            
        except Exception as e:
            print(f"❌ Failed to connect: {str(e)}")
            return False
    
    async def get_contacts(self):
        """Get all contacts and chats"""
        print("📋 Loading your contacts and chats...")
        contacts = []
        
        try:
            async for dialog in self.client.iter_dialogs():
                contact_type = "Unknown"
                if isinstance(dialog.entity, User):
                    if not dialog.entity.bot:
                        contact_type = "User"
                    else:
                        contact_type = "Bot"
                elif isinstance(dialog.entity, Chat):
                    contact_type = "Group"
                elif isinstance(dialog.entity, Channel):
                    contact_type = "Channel" if dialog.entity.broadcast else "Supergroup"
                
                contacts.append({
                    'id': dialog.entity.id,
                    'name': dialog.name or "Unknown",
                    'type': contact_type,
                    'entity': dialog.entity
                })
            
            print(f"✅ Loaded {len(contacts)} contacts/chats")
            return contacts
            
        except Exception as e:
            print(f"❌ Failed to load contacts: {str(e)}")
            return []
    
    def display_contacts(self, contacts):
        """Display contacts in a nice format"""
        print("\n" + "="*60)
        print("📱 YOUR CONTACTS & CHATS")
        print("="*60)
        
        users = [c for c in contacts if c['type'] == 'User']
        groups = [c for c in contacts if c['type'] in ['Group', 'Supergroup']]
        channels = [c for c in contacts if c['type'] == 'Channel']
        bots = [c for c in contacts if c['type'] == 'Bot']
        
        if users:
            print(f"\n👥 USERS ({len(users)}):")
            for i, contact in enumerate(users, 1):
                print(f"  {i:2d}. {contact['name']}")
        
        if groups:
            print(f"\n👨‍👩‍👧‍👦 GROUPS ({len(groups)}):")
            for i, contact in enumerate(groups, len(users) + 1):
                print(f"  {i:2d}. {contact['name']} ({contact['type']})")
        
        if channels:
            print(f"\n📢 CHANNELS ({len(channels)}):")
            for i, contact in enumerate(channels, len(users) + len(groups) + 1):
                print(f"  {i:2d}. {contact['name']}")
        
        if bots:
            print(f"\n🤖 BOTS ({len(bots)}):")
            for i, contact in enumerate(bots, len(users) + len(groups) + len(channels) + 1):
                print(f"  {i:2d}. {contact['name']}")
        
        print("="*60)
    
    def select_recipients(self, contacts):
        """Select recipients for messaging"""
        recipients = []
        
        print("\n📝 SELECT RECIPIENTS:")
        print("Enter numbers separated by commas (e.g., 1,3,5)")
        print("Or enter 'all' to select all contacts")
        print("Or enter 'range' for a range (e.g., 1-10)")
        
        while True:
            selection = input("\nYour selection: ").strip().lower()
            
            if selection == 'all':
                recipients = contacts.copy()
                break
            elif '-' in selection and selection.replace('-', '').isdigit():
                try:
                    start, end = map(int, selection.split('-'))
                    if 1 <= start <= len(contacts) and 1 <= end <= len(contacts):
                        recipients = contacts[start-1:end]
                        break
                    else:
                        print("❌ Range out of bounds!")
                except:
                    print("❌ Invalid range format!")
            else:
                try:
                    indices = [int(x.strip()) for x in selection.split(',')]
                    if all(1 <= i <= len(contacts) for i in indices):
                        recipients = [contacts[i-1] for i in indices]
                        break
                    else:
                        print("❌ Some numbers are out of range!")
                except:
                    print("❌ Invalid selection format!")
        
        print(f"\n✅ Selected {len(recipients)} recipients:")
        for recipient in recipients:
            print(f"  • {recipient['name']} ({recipient['type']})")
        
        return recipients
    
    def compose_message(self):
        """Compose message to send"""
        print("\n✍️  COMPOSE YOUR MESSAGE:")
        print("(Press Enter twice to finish, or type 'cancel' to abort)")
        
        lines = []
        empty_lines = 0
        
        while True:
            line = input()
            if line.lower() == 'cancel':
                return None
            
            if line == '':
                empty_lines += 1
                if empty_lines >= 2:
                    break
            else:
                empty_lines = 0
            
            lines.append(line)
        
        # Remove trailing empty lines
        while lines and lines[-1] == '':
            lines.pop()
        
        message = '\n'.join(lines)
        
        if not message.strip():
            print("❌ Message cannot be empty!")
            return None
        
        print(f"\n📝 Your message ({len(message)} characters):")
        print("-" * 40)
        print(message)
        print("-" * 40)
        
        confirm = input("\nSend this message? (y/n): ").strip().lower()
        return message if confirm == 'y' else None
    
    async def send_messages(self, recipients, message):
        """Send messages to selected recipients"""
        print(f"\n🚀 Sending message to {len(recipients)} recipients...")
        
        successful = 0
        failed = 0
        
        for i, recipient in enumerate(recipients, 1):
            try:
                print(f"[{i}/{len(recipients)}] Sending to {recipient['name']}...", end=' ')
                
                await self.client.send_message(recipient['entity'], message)
                print("✅")
                successful += 1
                
                # Rate limiting - wait between messages
                if i < len(recipients):
                    await asyncio.sleep(1)
                
            except FloodWaitError as e:
                print(f"⏳ Rate limited, waiting {e.seconds} seconds...")
                await asyncio.sleep(e.seconds)
                # Retry
                try:
                    await self.client.send_message(recipient['entity'], message)
                    print("✅ (retry successful)")
                    successful += 1
                except:
                    print("❌ (retry failed)")
                    failed += 1
                    
            except (PeerFloodError, UserPrivacyRestrictedError) as e:
                print("❌ (privacy/flood error)")
                failed += 1
                
            except Exception as e:
                print(f"❌ ({str(e)})")
                failed += 1
        
        print(f"\n📊 RESULTS:")
        print(f"✅ Successful: {successful}")
        print(f"❌ Failed: {failed}")
        print(f"📈 Success rate: {successful/(successful+failed)*100:.1f}%")
    
    async def main_menu(self):
        """Main application menu"""
        while True:
            print("\n" + "="*50)
            print("🚀 TELEGRAM DM BOT")
            print("="*50)
            print("1. 📤 Send messages to contacts")
            print("2. 🔄 Refresh contacts")
            print("3. 🚪 Exit")
            print("="*50)
            
            choice = input("Select option (1-3): ").strip()
            
            if choice == '1':
                contacts = await self.get_contacts()
                if not contacts:
                    print("❌ No contacts found!")
                    continue
                
                self.display_contacts(contacts)
                recipients = self.select_recipients(contacts)
                
                if not recipients:
                    print("❌ No recipients selected!")
                    continue
                
                message = self.compose_message()
                if not message:
                    print("❌ Message composition cancelled!")
                    continue
                
                await self.send_messages(recipients, message)
                
            elif choice == '2':
                await self.get_contacts()
                
            elif choice == '3':
                print("👋 Goodbye!")
                break
                
            else:
                print("❌ Invalid option!")
    
    async def run(self):
        """Run the bot"""
        print("🤖 Telegram DM Bot Starting...")
        
        if await self.connect():
            try:
                await self.main_menu()
            finally:
                if self.client:
                    await self.client.disconnect()
                    print("📱 Disconnected from Telegram")
        else:
            print("❌ Failed to start bot")

if __name__ == "__main__":
    bot = TelegramDMBot()
    asyncio.run(bot.run())
