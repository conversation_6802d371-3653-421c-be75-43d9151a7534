#!/usr/bin/env python3
"""
Telegram DM Bot - Windows GUI Application for Bulk Messaging
"""
import asyncio
import os
import sys
import webbrowser
import threading
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from typing import List, Dict, Optional
from telethon import TelegramClient
from telethon.tl.types import User, Chat, Channel
from telethon.errors import FloodWaitError, PeerFloodError, UserPrivacyRestrictedError
import time

class TelegramDMBotGUI:
    def __init__(self):
        self.client = None
        self.api_id = 23806500
        self.api_hash = "c9330b83e3e23125e890eaa41902b193"
        self.session_name = "telegram_dm_session"
        self.contacts = []
        self.groups = []
        self.is_connected = False

        # Create GUI
        self.root = tk.Tk()
        self.setup_gui()

    def setup_gui(self):
        """Setup the GUI interface"""
        self.root.title("Telegram DM Bot - Bulk Messaging Tool")
        self.root.geometry("800x600")
        self.root.configure(bg='#2b2b2b')

        # Create main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title
        title_label = tk.Label(main_frame, text="🚀 TELEGRAM DM BOT",
                              font=("Arial", 20, "bold"),
                              bg='#2b2b2b', fg='#ffffff')
        title_label.pack(pady=10)

        subtitle_label = tk.Label(main_frame, text="Bulk Messaging Tool - Send to 1000+ Groups",
                                 font=("Arial", 12),
                                 bg='#2b2b2b', fg='#cccccc')
        subtitle_label.pack(pady=5)

        # Connection frame
        conn_frame = ttk.LabelFrame(main_frame, text="Connection Status", padding=10)
        conn_frame.pack(fill=tk.X, pady=10)

        self.status_label = tk.Label(conn_frame, text="❌ Not Connected",
                                    font=("Arial", 10), fg='red')
        self.status_label.pack(side=tk.LEFT)

        self.connect_btn = tk.Button(conn_frame, text="Connect to Telegram",
                                    command=self.connect_telegram,
                                    bg='#4CAF50', fg='white', font=("Arial", 10))
        self.connect_btn.pack(side=tk.RIGHT)

        # Phone number frame (initially hidden)
        self.phone_frame = ttk.LabelFrame(main_frame, text="Login", padding=10)

        tk.Label(self.phone_frame, text="Phone Number (with country code):").pack(anchor=tk.W)
        self.phone_entry = tk.Entry(self.phone_frame, font=("Arial", 12), width=20)
        self.phone_entry.pack(fill=tk.X, pady=5)

        tk.Label(self.phone_frame, text="Verification Code:").pack(anchor=tk.W, pady=(10,0))
        self.code_entry = tk.Entry(self.phone_frame, font=("Arial", 12), width=20)
        self.code_entry.pack(fill=tk.X, pady=5)

        self.login_btn = tk.Button(self.phone_frame, text="Login",
                                  command=self.login_telegram,
                                  bg='#2196F3', fg='white', font=("Arial", 10))
        self.login_btn.pack(pady=10)

        # Main content frame (initially hidden)
        self.main_content = ttk.Frame(main_frame)

        # Notebook for tabs
        self.notebook = ttk.Notebook(self.main_content)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Bulk messaging tab
        self.bulk_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.bulk_frame, text="📤 Bulk Messaging")

        # Groups selection
        groups_label_frame = ttk.LabelFrame(self.bulk_frame, text="Select Groups", padding=10)
        groups_label_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Groups listbox with scrollbar
        list_frame = tk.Frame(groups_label_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        scrollbar = tk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.groups_listbox = tk.Listbox(list_frame, selectmode=tk.MULTIPLE,
                                        yscrollcommand=scrollbar.set,
                                        font=("Arial", 10))
        self.groups_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.groups_listbox.yview)

        # Selection buttons
        btn_frame = tk.Frame(groups_label_frame)
        btn_frame.pack(fill=tk.X, pady=5)

        tk.Button(btn_frame, text="Select All", command=self.select_all_groups,
                 bg='#FF9800', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="Clear Selection", command=self.clear_selection,
                 bg='#f44336', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="Refresh Groups", command=self.refresh_groups,
                 bg='#9C27B0', fg='white').pack(side=tk.LEFT, padx=5)

        # Message composition
        msg_frame = ttk.LabelFrame(self.bulk_frame, text="Compose Message", padding=10)
        msg_frame.pack(fill=tk.X, pady=5)

        self.message_text = scrolledtext.ScrolledText(msg_frame, height=6,
                                                     font=("Arial", 11))
        self.message_text.pack(fill=tk.X, pady=5)

        # Rate limiting
        rate_frame = ttk.LabelFrame(self.bulk_frame, text="Rate Limiting", padding=10)
        rate_frame.pack(fill=tk.X, pady=5)

        self.rate_var = tk.StringVar(value="1")
        rate_options = [("Fast (0.5s)", "0.5"), ("Normal (1s)", "1"),
                       ("Safe (2s)", "2"), ("Custom", "custom")]

        for text, value in rate_options:
            tk.Radiobutton(rate_frame, text=text, variable=self.rate_var,
                          value=value).pack(side=tk.LEFT, padx=10)

        self.custom_delay = tk.Entry(rate_frame, width=10)
        self.custom_delay.pack(side=tk.RIGHT)
        tk.Label(rate_frame, text="Custom delay (seconds):").pack(side=tk.RIGHT, padx=5)

        # Send button
        self.send_btn = tk.Button(self.bulk_frame, text="🚀 SEND BULK MESSAGES",
                                 command=self.send_bulk_messages,
                                 bg='#4CAF50', fg='white',
                                 font=("Arial", 14, "bold"), height=2)
        self.send_btn.pack(fill=tk.X, pady=10)

        # Progress frame
        self.progress_frame = ttk.LabelFrame(self.bulk_frame, text="Progress", padding=10)

        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=5)

        self.progress_label = tk.Label(self.progress_frame, text="Ready to send messages")
        self.progress_label.pack()

        # Statistics tab
        self.stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.stats_frame, text="📊 Statistics")

        self.stats_text = scrolledtext.ScrolledText(self.stats_frame,
                                                   font=("Courier", 11))
        self.stats_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Log tab
        self.log_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.log_frame, text="📝 Logs")

        self.log_text = scrolledtext.ScrolledText(self.log_frame,
                                                 font=("Courier", 10))
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        

    
    def log_message(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n")
        self.log_text.see(tk.END)
        self.root.update()

    def connect_telegram(self):
        """Start connection process"""
        self.connect_btn.config(state='disabled', text='Connecting...')
        self.phone_frame.pack(fill=tk.X, pady=10)
        self.log_message("Starting connection to Telegram...")

        # Run connection in thread
        thread = threading.Thread(target=self.connect_thread)
        thread.daemon = True
        thread.start()

    def connect_thread(self):
        """Connection thread"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.connect_async())
        except Exception as e:
            self.log_message(f"Connection error: {str(e)}")
            self.root.after(0, lambda: self.connect_btn.config(state='normal', text='Connect to Telegram'))

    async def connect_async(self):
        """Connect to Telegram"""
        try:
            self.client = TelegramClient(self.session_name, self.api_id, self.api_hash)
            self.log_message("Connecting to Telegram...")

            # Check if already logged in
            await self.client.connect()
            if await self.client.is_user_authorized():
                me = await self.client.get_me()
                self.log_message(f"Already logged in as: {me.first_name}")
                self.root.after(0, self.on_login_success)
                return True
            else:
                self.log_message("Need to login with phone number")
                return False

        except Exception as e:
            self.log_message(f"Failed to connect: {str(e)}")
            return False

    def login_telegram(self):
        """Login with phone number"""
        phone = self.phone_entry.get().strip()
        code = self.code_entry.get().strip()

        if not phone:
            messagebox.showerror("Error", "Please enter your phone number")
            return

        self.login_btn.config(state='disabled', text='Logging in...')

        # Run login in thread
        thread = threading.Thread(target=self.login_thread, args=(phone, code))
        thread.daemon = True
        thread.start()

    def login_thread(self, phone, code):
        """Login thread"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.login_async(phone, code))
        except Exception as e:
            self.log_message(f"Login error: {str(e)}")
            self.root.after(0, lambda: self.login_btn.config(state='normal', text='Login'))

    async def login_async(self, phone, code):
        """Async login"""
        try:
            if not code:
                # Send code
                await self.client.send_code_request(phone)
                self.log_message("Verification code sent to your phone")
                self.root.after(0, lambda: self.login_btn.config(state='normal', text='Enter Code & Login'))
                return

            # Login with code
            await self.client.sign_in(phone, code)
            me = await self.client.get_me()
            self.log_message(f"Successfully logged in as: {me.first_name}")
            self.root.after(0, self.on_login_success)

        except Exception as e:
            self.log_message(f"Login failed: {str(e)}")
            self.root.after(0, lambda: self.login_btn.config(state='normal', text='Login'))

    def on_login_success(self):
        """Called when login is successful"""
        self.is_connected = True
        self.status_label.config(text="✅ Connected", fg='green')
        self.phone_frame.pack_forget()
        self.main_content.pack(fill=tk.BOTH, expand=True, pady=10)
        self.refresh_groups()
    
    def refresh_groups(self):
        """Refresh groups list"""
        if not self.is_connected:
            return

        self.log_message("Loading groups...")
        thread = threading.Thread(target=self.refresh_groups_thread)
        thread.daemon = True
        thread.start()

    def refresh_groups_thread(self):
        """Refresh groups in thread"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.get_contacts_async())
        except Exception as e:
            self.log_message(f"Error loading groups: {str(e)}")

    async def get_contacts_async(self):
        """Get all contacts and chats"""
        self.log_message("Loading your contacts and chats...")
        contacts = []
        groups = []

        try:
            async for dialog in self.client.iter_dialogs():
                contact_type = "Unknown"
                if isinstance(dialog.entity, User):
                    if not dialog.entity.bot:
                        contact_type = "User"
                    else:
                        contact_type = "Bot"
                elif isinstance(dialog.entity, Chat):
                    contact_type = "Group"
                elif isinstance(dialog.entity, Channel):
                    contact_type = "Channel" if dialog.entity.broadcast else "Supergroup"

                contact = {
                    'id': dialog.entity.id,
                    'name': dialog.name or "Unknown",
                    'type': contact_type,
                    'entity': dialog.entity
                }

                contacts.append(contact)

                # Add to groups list if it's a group
                if contact_type in ['Group', 'Supergroup']:
                    groups.append(contact)

            self.contacts = contacts
            self.groups = groups

            self.log_message(f"Loaded {len(contacts)} contacts ({len(groups)} groups)")

            # Update GUI
            self.root.after(0, self.update_groups_list)
            self.root.after(0, self.update_statistics)

        except Exception as e:
            self.log_message(f"Failed to load contacts: {str(e)}")

    def update_groups_list(self):
        """Update groups listbox"""
        self.groups_listbox.delete(0, tk.END)
        for group in self.groups:
            self.groups_listbox.insert(tk.END, f"{group['name']} ({group['type']})")

    def update_statistics(self):
        """Update statistics tab"""
        users = [c for c in self.contacts if c['type'] == 'User']
        groups = [c for c in self.contacts if c['type'] in ['Group', 'Supergroup']]
        channels = [c for c in self.contacts if c['type'] == 'Channel']
        bots = [c for c in self.contacts if c['type'] == 'Bot']

        stats = f"""📊 CONTACT STATISTICS
{'='*50}
👥 Users: {len(users)}
👨‍👩‍👧‍👦 Groups: {len(groups)}
📢 Channels: {len(channels)}
🤖 Bots: {len(bots)}
📱 Total: {len(self.contacts)}
{'='*50}

🎯 BULK MESSAGING POTENTIAL:
• You can send to {len(groups)} groups at once
• Estimated time (1s delay): {len(groups)} seconds
• Estimated time (2s delay): {len(groups)*2} seconds
"""

        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats)

    def select_all_groups(self):
        """Select all groups"""
        self.groups_listbox.select_set(0, tk.END)

    def clear_selection(self):
        """Clear group selection"""
        self.groups_listbox.selection_clear(0, tk.END)
    
    def send_bulk_messages(self):
        """Send bulk messages"""
        selected_indices = self.groups_listbox.curselection()
        if not selected_indices:
            messagebox.showerror("Error", "Please select at least one group")
            return

        message = self.message_text.get(1.0, tk.END).strip()
        if not message:
            messagebox.showerror("Error", "Please enter a message")
            return

        selected_groups = [self.groups[i] for i in selected_indices]

        # Confirm sending
        result = messagebox.askyesno("Confirm",
                                   f"Send message to {len(selected_groups)} groups?\n\n"
                                   f"Message preview:\n{message[:100]}...")
        if not result:
            return

        # Get delay
        delay = 1.0
        if self.rate_var.get() == "custom":
            try:
                delay = float(self.custom_delay.get())
            except:
                delay = 1.0
        else:
            delay = float(self.rate_var.get())

        # Disable send button
        self.send_btn.config(state='disabled', text='Sending...')
        self.progress_frame.pack(fill=tk.X, pady=5)

        # Start sending in thread
        thread = threading.Thread(target=self.send_messages_thread,
                                 args=(selected_groups, message, delay))
        thread.daemon = True
        thread.start()

    def send_messages_thread(self, groups, message, delay):
        """Send messages in thread"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.send_messages_async(groups, message, delay))
        except Exception as e:
            self.log_message(f"Sending error: {str(e)}")
        finally:
            self.root.after(0, lambda: self.send_btn.config(state='normal', text='🚀 SEND BULK MESSAGES'))

    async def send_messages_async(self, groups, message, delay):
        """Send messages async"""
        successful = 0
        failed = 0
        total = len(groups)

        self.root.after(0, lambda: self.progress_bar.config(maximum=total, value=0))

        for i, group in enumerate(groups, 1):
            try:
                self.root.after(0, lambda i=i, name=group['name']:
                               self.progress_label.config(text=f"Sending to {name} ({i}/{total})"))

                await self.client.send_message(group['entity'], message)
                successful += 1
                self.log_message(f"✅ Sent to {group['name']}")

                # Update progress
                self.root.after(0, lambda i=i: self.progress_bar.config(value=i))

                # Rate limiting
                if i < total:
                    await asyncio.sleep(delay)

            except FloodWaitError as e:
                self.log_message(f"⏳ Rate limited, waiting {e.seconds} seconds...")
                await asyncio.sleep(e.seconds)
                # Retry
                try:
                    await self.client.send_message(group['entity'], message)
                    successful += 1
                    self.log_message(f"✅ Sent to {group['name']} (retry)")
                except:
                    failed += 1
                    self.log_message(f"❌ Failed to send to {group['name']} (retry failed)")

            except (PeerFloodError, UserPrivacyRestrictedError):
                failed += 1
                self.log_message(f"❌ Privacy/flood error for {group['name']}")

            except Exception as e:
                failed += 1
                self.log_message(f"❌ Error sending to {group['name']}: {str(e)}")

        # Show results
        success_rate = (successful/(successful+failed)*100) if (successful+failed) > 0 else 0
        result_msg = f"""📊 BULK MESSAGING RESULTS
✅ Successful: {successful}
❌ Failed: {failed}
📈 Success rate: {success_rate:.1f}%"""

        self.log_message(result_msg)
        self.root.after(0, lambda: self.progress_label.config(text="Bulk messaging completed!"))
        self.root.after(0, lambda: messagebox.showinfo("Results", result_msg))

    def run(self):
        """Run the GUI application"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("Application stopped by user")
        except Exception as e:
            self.log_message(f"Unexpected error: {str(e)}")
        finally:
            if self.client and self.is_connected:
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.client.disconnect())
                except:
                    pass

# Legacy console methods (kept for compatibility but not used in GUI)
class TelegramDMBot:
    def __init__(self):
        pass

    def display_contacts(self, contacts, show_all=True):
        """Display contacts in a nice format"""
        print("\n" + "="*60)
        print("📱 YOUR CONTACTS & CHATS")
        print("="*60)

        users = [c for c in contacts if c['type'] == 'User']
        groups = [c for c in contacts if c['type'] in ['Group', 'Supergroup']]
        channels = [c for c in contacts if c['type'] == 'Channel']
        bots = [c for c in contacts if c['type'] == 'Bot']

        if users and show_all:
            print(f"\n👥 USERS ({len(users)}):")
            if len(users) <= 50:
                for i, contact in enumerate(users, 1):
                    print(f"  {i:2d}. {contact['name']}")
            else:
                for i, contact in enumerate(users[:25], 1):
                    print(f"  {i:2d}. {contact['name']}")
                print(f"  ... and {len(users)-25} more users")

        if groups:
            print(f"\n👨‍👩‍👧‍👦 GROUPS ({len(groups)}):")
            start_idx = len(users) + 1 if show_all else 1
            if len(groups) <= 50:
                for i, contact in enumerate(groups, start_idx):
                    print(f"  {i:2d}. {contact['name']} ({contact['type']})")
            else:
                for i, contact in enumerate(groups[:25], start_idx):
                    print(f"  {i:2d}. {contact['name']} ({contact['type']})")
                print(f"  ... and {len(groups)-25} more groups")

        if channels and show_all:
            print(f"\n📢 CHANNELS ({len(channels)}):")
            start_idx = len(users) + len(groups) + 1
            if len(channels) <= 50:
                for i, contact in enumerate(channels, start_idx):
                    print(f"  {i:2d}. {contact['name']}")
            else:
                for i, contact in enumerate(channels[:25], start_idx):
                    print(f"  {i:2d}. {contact['name']}")
                print(f"  ... and {len(channels)-25} more channels")

        if bots and show_all:
            print(f"\n🤖 BOTS ({len(bots)}):")
            start_idx = len(users) + len(groups) + len(channels) + 1
            if len(bots) <= 50:
                for i, contact in enumerate(bots, start_idx):
                    print(f"  {i:2d}. {contact['name']}")
            else:
                for i, contact in enumerate(bots[:25], start_idx):
                    print(f"  {i:2d}. {contact['name']}")
                print(f"  ... and {len(bots)-25} more bots")

        print("="*60)
    
    def select_recipients(self, contacts):
        """Select recipients for messaging"""
        recipients = []
        
        print("\n📝 SELECT RECIPIENTS:")
        print("Enter numbers separated by commas (e.g., 1,3,5)")
        print("Or enter 'all' to select all contacts")
        print("Or enter 'range' for a range (e.g., 1-10)")
        
        while True:
            selection = input("\nYour selection: ").strip().lower()
            
            if selection == 'all':
                recipients = contacts.copy()
                break
            elif '-' in selection and selection.replace('-', '').isdigit():
                try:
                    start, end = map(int, selection.split('-'))
                    if 1 <= start <= len(contacts) and 1 <= end <= len(contacts):
                        recipients = contacts[start-1:end]
                        break
                    else:
                        print("❌ Range out of bounds!")
                except:
                    print("❌ Invalid range format!")
            else:
                try:
                    indices = [int(x.strip()) for x in selection.split(',')]
                    if all(1 <= i <= len(contacts) for i in indices):
                        recipients = [contacts[i-1] for i in indices]
                        break
                    else:
                        print("❌ Some numbers are out of range!")
                except:
                    print("❌ Invalid selection format!")
        
        print(f"\n✅ Selected {len(recipients)} recipients:")
        for recipient in recipients:
            print(f"  • {recipient['name']} ({recipient['type']})")
        
        return recipients
    
    def compose_message(self):
        """Compose message to send"""
        print("\n✍️  COMPOSE YOUR MESSAGE:")
        print("(Press Enter twice to finish, or type 'cancel' to abort)")
        
        lines = []
        empty_lines = 0
        
        while True:
            line = input()
            if line.lower() == 'cancel':
                return None
            
            if line == '':
                empty_lines += 1
                if empty_lines >= 2:
                    break
            else:
                empty_lines = 0
            
            lines.append(line)
        
        # Remove trailing empty lines
        while lines and lines[-1] == '':
            lines.pop()
        
        message = '\n'.join(lines)
        
        if not message.strip():
            print("❌ Message cannot be empty!")
            return None
        
        print(f"\n📝 Your message ({len(message)} characters):")
        print("-" * 40)
        print(message)
        print("-" * 40)
        
        confirm = input("\nSend this message? (y/n): ").strip().lower()
        return message if confirm == 'y' else None
    
    async def send_messages(self, recipients, message, delay=1):
        """Send messages to selected recipients"""
        print(f"\n🚀 Sending message to {len(recipients)} recipients...")

        successful = 0
        failed = 0
        errors = []

        for i, recipient in enumerate(recipients, 1):
            try:
                print(f"[{i}/{len(recipients)}] Sending to {recipient['name']}...", end=' ')

                await self.client.send_message(recipient['entity'], message)
                print("✅")
                successful += 1

                # Rate limiting - wait between messages
                if i < len(recipients):
                    await asyncio.sleep(delay)

            except FloodWaitError as e:
                print(f"⏳ Rate limited, waiting {e.seconds} seconds...")
                await asyncio.sleep(e.seconds)
                # Retry
                try:
                    await self.client.send_message(recipient['entity'], message)
                    print("✅ (retry successful)")
                    successful += 1
                except Exception as retry_error:
                    print("❌ (retry failed)")
                    failed += 1
                    errors.append(f"{recipient['name']}: {str(retry_error)}")

            except (PeerFloodError, UserPrivacyRestrictedError) as e:
                print("❌ (privacy/flood error)")
                failed += 1
                errors.append(f"{recipient['name']}: {str(e)}")

            except Exception as e:
                print(f"❌ ({str(e)})")
                failed += 1
                errors.append(f"{recipient['name']}: {str(e)}")

        print(f"\n📊 RESULTS:")
        print(f"✅ Successful: {successful}")
        print(f"❌ Failed: {failed}")
        if successful + failed > 0:
            print(f"📈 Success rate: {successful/(successful+failed)*100:.1f}%")

        # Show first few errors if any
        if errors and len(errors) <= 5:
            print(f"\n❌ Errors:")
            for error in errors:
                print(f"  • {error}")
        elif errors:
            print(f"\n❌ First 5 errors (total: {len(errors)}):")
            for error in errors[:5]:
                print(f"  • {error}")

    async def send_to_all_groups(self):
        """Send message to all groups with bulk options"""
        contacts = await self.get_contacts()
        if not contacts:
            print("❌ No contacts found!")
            return

        groups = [c for c in contacts if c['type'] in ['Group', 'Supergroup']]
        if not groups:
            print("❌ No groups found!")
            return

        print(f"\n👨‍👩‍👧‍👦 Found {len(groups)} groups")

        # Show groups with pagination
        self.display_contacts(contacts, show_all=False)

        print(f"\n📤 BULK GROUP MESSAGING OPTIONS:")
        print("1. 🎯 Send to ALL groups")
        print("2. 📝 Select specific groups")
        print("3. 📊 Send to groups by range")
        print("4. 🔍 Search and select groups")
        print("5. 🔙 Back to main menu")

        choice = input("\nSelect option (1-5): ").strip()

        if choice == '1':
            # Send to all groups
            print(f"\n⚠️  You are about to send a message to ALL {len(groups)} groups!")
            confirm = input("Are you sure? Type 'YES' to confirm: ").strip()
            if confirm != 'YES':
                print("❌ Operation cancelled!")
                return

            message = self.compose_message()
            if not message:
                return

            # Ask for delay between messages
            print(f"\n⏱️  RATE LIMITING:")
            print("1. Fast (0.5s delay) - Risk of rate limiting")
            print("2. Normal (1s delay) - Recommended")
            print("3. Safe (2s delay) - Very safe")
            print("4. Custom delay")

            delay_choice = input("Select delay option (1-4): ").strip()
            delay = 1  # default

            if delay_choice == '1':
                delay = 0.5
            elif delay_choice == '2':
                delay = 1
            elif delay_choice == '3':
                delay = 2
            elif delay_choice == '4':
                try:
                    delay = float(input("Enter delay in seconds: "))
                except:
                    delay = 1

            await self.send_messages(groups, message, delay)

        elif choice == '2':
            # Select specific groups
            recipients = self.select_recipients(groups)
            if not recipients:
                return

            message = self.compose_message()
            if not message:
                return

            await self.send_messages(recipients, message)

        elif choice == '3':
            # Send by range
            print(f"\nEnter range (1-{len(groups)}):")
            try:
                start = int(input("Start index: "))
                end = int(input("End index: "))

                if 1 <= start <= len(groups) and 1 <= end <= len(groups) and start <= end:
                    selected_groups = groups[start-1:end]
                    print(f"\n✅ Selected {len(selected_groups)} groups (#{start} to #{end})")

                    message = self.compose_message()
                    if not message:
                        return

                    await self.send_messages(selected_groups, message)
                else:
                    print("❌ Invalid range!")
            except ValueError:
                print("❌ Invalid input!")

        elif choice == '4':
            # Search groups
            search_term = input("\nEnter search term: ").strip().lower()
            if not search_term:
                print("❌ Search term cannot be empty!")
                return

            matching_groups = [g for g in groups if search_term in g['name'].lower()]

            if not matching_groups:
                print(f"❌ No groups found matching '{search_term}'")
                return

            print(f"\n🔍 Found {len(matching_groups)} groups matching '{search_term}':")
            for i, group in enumerate(matching_groups, 1):
                print(f"  {i:2d}. {group['name']} ({group['type']})")

            print(f"\nSend to all {len(matching_groups)} matching groups?")
            confirm = input("Type 'yes' to confirm: ").strip().lower()

            if confirm == 'yes':
                message = self.compose_message()
                if not message:
                    return

                await self.send_messages(matching_groups, message)
            else:
                print("❌ Operation cancelled!")

        elif choice == '5':
            return
        else:
            print("❌ Invalid option!")
    
    async def main_menu(self):
        """Main application menu"""
        while True:
            print("\n" + "="*60)
            print("🚀 TELEGRAM DM BOT - BULK MESSAGING")
            print("="*60)
            print("1. 📤 Send messages to selected contacts")
            print("2. 👨‍👩‍👧‍👦 Bulk send to groups (FAST)")
            print("3. 📊 View contact statistics")
            print("4. 🔄 Refresh contacts")
            print("5. 🚪 Exit")
            print("="*60)

            choice = input("Select option (1-5): ").strip()

            if choice == '1':
                contacts = await self.get_contacts()
                if not contacts:
                    print("❌ No contacts found!")
                    continue

                self.display_contacts(contacts)
                recipients = self.select_recipients(contacts)

                if not recipients:
                    print("❌ No recipients selected!")
                    continue

                message = self.compose_message()
                if not message:
                    print("❌ Message composition cancelled!")
                    continue

                await self.send_messages(recipients, message)

            elif choice == '2':
                await self.send_to_all_groups()

            elif choice == '3':
                contacts = await self.get_contacts()
                if contacts:
                    self.show_statistics(contacts)

            elif choice == '4':
                await self.get_contacts()

            elif choice == '5':
                print("👋 Goodbye!")
                break

            else:
                print("❌ Invalid option!")

    def show_statistics(self, contacts):
        """Show contact statistics"""
        users = [c for c in contacts if c['type'] == 'User']
        groups = [c for c in contacts if c['type'] in ['Group', 'Supergroup']]
        channels = [c for c in contacts if c['type'] == 'Channel']
        bots = [c for c in contacts if c['type'] == 'Bot']

        print("\n" + "="*50)
        print("📊 CONTACT STATISTICS")
        print("="*50)
        print(f"👥 Users: {len(users)}")
        print(f"👨‍👩‍👧‍👦 Groups: {len(groups)}")
        print(f"📢 Channels: {len(channels)}")
        print(f"🤖 Bots: {len(bots)}")
        print(f"📱 Total: {len(contacts)}")
        print("="*50)

        if groups:
            print(f"\n🎯 BULK MESSAGING POTENTIAL:")
            print(f"   • You can send to {len(groups)} groups at once")
            print(f"   • Estimated time (1s delay): {len(groups)} seconds")
            print(f"   • Estimated time (2s delay): {len(groups)*2} seconds")
    
    def show_welcome(self):
        """Show welcome screen"""
        # Clear screen for Windows
        os.system('cls' if os.name == 'nt' else 'clear')

        print("\n" + "="*70)
        print("🚀 TELEGRAM DM BOT - BULK MESSAGING TOOL")
        print("="*70)
        print("📱 Send messages to 1000+ groups efficiently!")
        print("🎯 Perfect for bulk messaging, announcements, and marketing")
        print("⚡ Fast, safe, and user-friendly")
        print("="*70)

        print("\n🔧 READY TO START:")
        print("✅ API credentials configured (PumpX Bot)")
        print("📱 Just login with your Telegram account")
        print("🚀 Start bulk messaging immediately!")

        print("\n📋 WHAT YOU'LL NEED:")
        print("• Your Telegram phone number")
        print("• Access to your phone for verification code")

        print("\n💻 WINDOWS DESKTOP APP VERSION")
        print("📦 Standalone executable - no Python required!")

        print("\n" + "="*70)
        input("Press Enter to login and start...")

    async def run(self):
        """Run the bot"""
        try:
            self.show_welcome()

            if await self.connect():
                try:
                    await self.main_menu()
                finally:
                    if self.client:
                        await self.client.disconnect()
                        print("📱 Disconnected from Telegram")
            else:
                print("❌ Failed to start bot")
        except KeyboardInterrupt:
            print("\n\n👋 Bot stopped by user")
        except Exception as e:
            print(f"\n❌ Unexpected error: {str(e)}")
        finally:
            print("\n🔚 Press Enter to exit...")
            input()

if __name__ == "__main__":
    # Create and run GUI application
    app = TelegramDMBotGUI()
    app.run()
