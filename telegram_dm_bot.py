#!/usr/bin/env python3
"""
Telegram DM Bot - Windows GUI Application for Bulk Messaging
"""
import asyncio
import os
import sys
import webbrowser
import threading
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from typing import List, Dict, Optional
from telethon import TelegramClient
from telethon.tl.types import User, Chat, Channel
from telethon.errors import FloodWaitError, PeerFloodError, UserPrivacyRestrictedError
from telethon.errors import SessionPasswordNeededError, InviteHashExpiredError, InviteHashInvalidError, UserAlreadyParticipantError
from telethon.tl.functions.channels import JoinChannelRequest
from telethon.tl.functions.messages import ImportChatInviteRequest, CheckChatInviteRequest
from telethon.tl.types import ChatInviteAlready, ChatInvite
import time

class TelegramDMBotGUI:
    def __init__(self):
        self.client = None
        self.api_id = 23806500
        self.api_hash = "c9330b83e3e23125e890eaa41902b193"
        self.session_name = "telegram_dm_session"
        self.contacts = []
        self.groups = []
        self.is_connected = False
        self.event_loop = None
        self.loop_thread = None

        # Create GUI
        self.root = tk.Tk()
        self.setup_gui()

        # Start event loop thread
        self.start_event_loop_thread()

    def start_event_loop_thread(self):
        """Start a dedicated event loop thread"""
        def run_event_loop():
            try:
                self.event_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self.event_loop)
                self.log_message("Event loop thread started")
                self.event_loop.run_forever()
            except Exception as e:
                self.log_message(f"Error in event loop thread: {e}")

        self.loop_thread = threading.Thread(target=run_event_loop, daemon=True)
        self.loop_thread.start()

        # Wait for loop to start
        import time
        for i in range(20):  # Wait up to 2 seconds
            time.sleep(0.1)
            if self.event_loop is not None:
                break

        if self.event_loop:
            self.log_message("✅ Dedicated event loop ready")
        else:
            self.log_message("❌ Failed to start event loop")

    def setup_gui(self):
        """Setup the modern dashboard GUI interface"""
        self.root.title("🚀 TELEGRAM DM BOT - Professional Marketing Dashboard")
        self.root.geometry("1400x900")  # Much larger for dashboard
        self.root.configure(bg='#1a1a1a')
        self.root.state('zoomed')  # Start maximized

        # Apply modern styling
        self.setup_modern_styles()

        # Create main dashboard layout
        self.create_dashboard_layout()

    def setup_modern_styles(self):
        """Setup modern CSS-like styling"""
        # Configure ttk styles for modern look
        style = ttk.Style()
        style.theme_use('clam')

        # Modern dark theme colors
        self.colors = {
            'bg_primary': '#1a1a1a',      # Main background
            'bg_secondary': '#2d2d2d',    # Card backgrounds
            'bg_accent': '#3d3d3d',       # Hover/active states
            'text_primary': '#ffffff',    # Main text
            'text_secondary': '#b0b0b0',  # Secondary text
            'accent_blue': '#0078d4',     # Primary buttons
            'accent_green': '#107c10',    # Success/positive
            'accent_red': '#d13438',      # Error/negative
            'accent_orange': '#ff8c00',   # Warning/info
            'accent_purple': '#8b5cf6',   # Special features
            'border': '#404040'           # Borders
        }

        # Configure modern button styles
        style.configure('Modern.TButton',
                       background=self.colors['accent_blue'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8))

        style.configure('Success.TButton',
                       background=self.colors['accent_green'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8))

        style.configure('Danger.TButton',
                       background=self.colors['accent_red'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8))

        style.configure('Warning.TButton',
                       background=self.colors['accent_orange'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8))

        # Configure frame styles
        style.configure('Card.TFrame',
                       background=self.colors['bg_secondary'],
                       relief='flat',
                       borderwidth=1)

        style.configure('Modern.TLabelframe',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'],
                       borderwidth=1,
                       relief='flat')

        style.configure('Modern.TLabelframe.Label',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'],
                       font=('Segoe UI', 11, 'bold'))

    def create_dashboard_layout(self):
        """Create the main dashboard layout"""
        # Top header bar
        self.create_header()

        # Main content area with 3 columns
        self.create_main_content()

        # Bottom status bar
        self.create_status_bar()

    def create_header(self):
        """Create the top header with title and main controls"""
        header_frame = tk.Frame(self.root, bg=self.colors['bg_secondary'], height=80)
        header_frame.pack(fill=tk.X, padx=0, pady=0)
        header_frame.pack_propagate(False)

        # Left side - Title and status
        left_header = tk.Frame(header_frame, bg=self.colors['bg_secondary'])
        left_header.pack(side=tk.LEFT, fill=tk.Y, padx=20, pady=15)

        title_label = tk.Label(left_header,
                              text="🚀 TELEGRAM DM BOT",
                              font=("Segoe UI", 20, "bold"),
                              bg=self.colors['bg_secondary'],
                              fg=self.colors['text_primary'])
        title_label.pack(anchor=tk.W)

        self.status_label = tk.Label(left_header,
                                    text="🔴 Disconnected",
                                    font=("Segoe UI", 12),
                                    bg=self.colors['bg_secondary'],
                                    fg=self.colors['accent_red'])
        self.status_label.pack(anchor=tk.W)

        # Right side - Main action buttons
        right_header = tk.Frame(header_frame, bg=self.colors['bg_secondary'])
        right_header.pack(side=tk.RIGHT, fill=tk.Y, padx=20, pady=15)

        # Connection button
        self.connect_btn = tk.Button(right_header,
                                    text="🔗 CONNECT TO TELEGRAM",
                                    command=self.connect_to_telegram,
                                    bg=self.colors['accent_blue'],
                                    fg='white',
                                    font=("Segoe UI", 12, "bold"),
                                    relief='flat',
                                    padx=20, pady=10)
        self.connect_btn.pack(side=tk.RIGHT, padx=5)

        # Automation toggle
        self.automation_toggle = tk.Button(right_header,
                                          text="🔄 START AUTOMATION",
                                          command=self.toggle_automation,
                                          bg=self.colors['accent_green'],
                                          fg='white',
                                          font=("Segoe UI", 12, "bold"),
                                          relief='flat',
                                          padx=20, pady=10,
                                          state='disabled')
        self.automation_toggle.pack(side=tk.RIGHT, padx=5)

    def create_main_content(self):
        """Create the main 3-column dashboard content"""
        main_container = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Left column - Message Composer & Groups
        self.left_column = tk.Frame(main_container, bg=self.colors['bg_primary'], width=450)
        self.left_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=5)
        self.left_column.pack_propagate(False)

        # Middle column - Live Monitor & Analytics
        self.middle_column = tk.Frame(main_container, bg=self.colors['bg_primary'])
        self.middle_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)

        # Right column - Discovery & Controls
        self.right_column = tk.Frame(main_container, bg=self.colors['bg_primary'], width=400)
        self.right_column.pack(side=tk.RIGHT, fill=tk.BOTH, expand=False, padx=5)
        self.right_column.pack_propagate(False)

        # Setup each column
        self.setup_left_column()
        self.setup_middle_column()
        self.setup_right_column()

    def create_status_bar(self):
        """Create the bottom status bar"""
        status_bar = tk.Frame(self.root, bg=self.colors['bg_secondary'], height=30)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        status_bar.pack_propagate(False)

        # Status info
        self.stats_label = tk.Label(status_bar,
                                   text="Ready • 0 groups loaded • 0 messages sent",
                                   font=("Segoe UI", 10),
                                   bg=self.colors['bg_secondary'],
                                   fg=self.colors['text_secondary'])
        self.stats_label.pack(side=tk.LEFT, padx=15, pady=5)

        # Version info
        version_label = tk.Label(status_bar,
                                text="v2.0 Professional",
                                font=("Segoe UI", 10),
                                bg=self.colors['bg_secondary'],
                                fg=self.colors['text_secondary'])
        version_label.pack(side=tk.RIGHT, padx=15, pady=5)

    def setup_left_column(self):
        """Setup left column - Message Composer & Groups"""
        # Message Composer Card
        composer_card = self.create_card(self.left_column, "📝 Message Composer")

        # Message type selection
        type_frame = tk.Frame(composer_card, bg=self.colors['bg_secondary'])
        type_frame.pack(fill=tk.X, pady=5)

        tk.Label(type_frame, text="Message Type:",
                font=("Segoe UI", 10, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W)

        self.msg_type_var = tk.StringVar(value="text")
        type_buttons_frame = tk.Frame(type_frame, bg=self.colors['bg_secondary'])
        type_buttons_frame.pack(fill=tk.X, pady=5)

        msg_types = [("📄 Text", "text"), ("🖼️ Photo", "photo"), ("🔗 Link", "link")]
        for text, value in msg_types:
            btn = tk.Radiobutton(type_buttons_frame, text=text, variable=self.msg_type_var,
                               value=value, command=self.on_message_type_change,
                               bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                               selectcolor=self.colors['accent_blue'],
                               font=("Segoe UI", 10))
            btn.pack(side=tk.LEFT, padx=10)

        # Photo selection (initially hidden)
        self.photo_frame = tk.Frame(composer_card, bg=self.colors['bg_secondary'])

        photo_btn_frame = tk.Frame(self.photo_frame, bg=self.colors['bg_secondary'])
        photo_btn_frame.pack(fill=tk.X, pady=5)

        self.photo_btn = tk.Button(photo_btn_frame, text="📁 Select Photo",
                                  command=self.select_photo,
                                  bg=self.colors['accent_orange'], fg='white',
                                  font=("Segoe UI", 10), relief='flat', padx=15, pady=5)
        self.photo_btn.pack(side=tk.LEFT)

        self.photo_label = tk.Label(photo_btn_frame, text="No photo selected",
                                   bg=self.colors['bg_secondary'], fg=self.colors['text_secondary'],
                                   font=("Segoe UI", 9))
        self.photo_label.pack(side=tk.LEFT, padx=10)

        # Message text area
        tk.Label(composer_card, text="Message Content:",
                font=("Segoe UI", 10, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W, pady=(10,5))

        self.message_text = scrolledtext.ScrolledText(composer_card, height=8,
                                                     font=("Segoe UI", 11),
                                                     bg='#2a2a2a', fg='white',
                                                     insertbackground='white')
        self.message_text.pack(fill=tk.X, pady=5)
        self.message_text.bind('<KeyRelease>', self.update_char_count)

        # Character counter
        self.char_count_label = tk.Label(composer_card, text="0/4096 characters",
                                        bg=self.colors['bg_secondary'],
                                        fg=self.colors['text_secondary'],
                                        font=("Segoe UI", 9))
        self.char_count_label.pack(anchor=tk.E, pady=2)

        # Quick templates
        templates_frame = tk.Frame(composer_card, bg=self.colors['bg_secondary'])
        templates_frame.pack(fill=tk.X, pady=10)

        tk.Label(templates_frame, text="Quick Templates:",
                font=("Segoe UI", 10, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W)

        template_btns_frame = tk.Frame(templates_frame, bg=self.colors['bg_secondary'])
        template_btns_frame.pack(fill=tk.X, pady=5)

        templates = [
            ("🚀 Crypto", "🚀 New Token Alert! 🚀\n\n💎 Token: [TOKEN_NAME]\n📈 Price: $[PRICE]\n🔗 Website: [WEBSITE]\n\n#crypto #token #investment"),
            ("💰 Trading", "💰 Trading Signal Alert! 💰\n\n📊 Pair: [PAIR]\n📈 Entry: [ENTRY_PRICE]\n🎯 Target: [TARGET]\n🛑 Stop Loss: [STOP_LOSS]\n\n#trading #signal"),
            ("🎯 Promo", "🎯 Special Promotion! 🎯\n\n🔥 [OFFER_TITLE]\n💸 Discount: [DISCOUNT]%\n⏰ Limited Time Only!\n🔗 Link: [WEBSITE]\n\n#promo #discount #sale")
        ]

        for name, template in templates:
            btn = tk.Button(template_btns_frame, text=name,
                           command=lambda t=template: self.insert_template(t),
                           bg=self.colors['accent_purple'], fg='white',
                           font=("Segoe UI", 9), relief='flat', padx=10, pady=3)
            btn.pack(side=tk.LEFT, padx=3)

        # Send button
        send_frame = tk.Frame(composer_card, bg=self.colors['bg_secondary'])
        send_frame.pack(fill=tk.X, pady=15)

        self.send_btn = tk.Button(send_frame, text="🚀 SEND TO SELECTED GROUPS",
                                 command=self.send_bulk_messages,
                                 bg=self.colors['accent_green'], fg='white',
                                 font=("Segoe UI", 14, "bold"),
                                 relief='flat', padx=30, pady=15)
        self.send_btn.pack(fill=tk.X)

        # Groups Card
        groups_card = self.create_card(self.left_column, "👥 Groups Manager")

        # Group controls
        group_controls = tk.Frame(groups_card, bg=self.colors['bg_secondary'])
        group_controls.pack(fill=tk.X, pady=5)

        tk.Button(group_controls, text="🔄 Refresh",
                 command=self.refresh_groups,
                 bg=self.colors['accent_blue'], fg='white',
                 font=("Segoe UI", 9), relief='flat', padx=10, pady=5).pack(side=tk.LEFT, padx=2)

        tk.Button(group_controls, text="✅ Select All",
                 command=self.select_all_groups,
                 bg=self.colors['accent_blue'], fg='white',
                 font=("Segoe UI", 9), relief='flat', padx=10, pady=5).pack(side=tk.LEFT, padx=2)

        tk.Button(group_controls, text="❌ Clear",
                 command=self.clear_selection,
                 bg=self.colors['accent_red'], fg='white',
                 font=("Segoe UI", 9), relief='flat', padx=10, pady=5).pack(side=tk.LEFT, padx=2)

        # Groups list
        self.groups_listbox = tk.Listbox(groups_card, height=12,
                                        selectmode=tk.MULTIPLE,
                                        bg='#2a2a2a', fg='white',
                                        font=("Segoe UI", 10),
                                        selectbackground=self.colors['accent_blue'])
        self.groups_listbox.pack(fill=tk.BOTH, expand=True, pady=5)

        # Group stats
        self.group_stats_label = tk.Label(groups_card, text="0 groups loaded",
                                         bg=self.colors['bg_secondary'],
                                         fg=self.colors['text_secondary'],
                                         font=("Segoe UI", 9))
        self.group_stats_label.pack(anchor=tk.W, pady=2)

    def create_card(self, parent, title):
        """Create a modern card widget"""
        card_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], relief='flat', bd=1)
        card_frame.pack(fill=tk.X, pady=8, padx=5)

        # Card header
        header = tk.Frame(card_frame, bg=self.colors['bg_accent'], height=35)
        header.pack(fill=tk.X)
        header.pack_propagate(False)

        title_label = tk.Label(header, text=title,
                              font=("Segoe UI", 12, "bold"),
                              bg=self.colors['bg_accent'],
                              fg=self.colors['text_primary'])
        title_label.pack(side=tk.LEFT, padx=15, pady=8)

        # Card content
        content = tk.Frame(card_frame, bg=self.colors['bg_secondary'])
        content.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        return content

    def setup_middle_column(self):
        """Setup middle column - Live Monitor & Analytics"""
        # Live Monitor Card
        monitor_card = self.create_card(self.middle_column, "📺 Live Activity Monitor")

        # Monitor controls
        monitor_controls = tk.Frame(monitor_card, bg=self.colors['bg_secondary'])
        monitor_controls.pack(fill=tk.X, pady=5)

        tk.Button(monitor_controls, text="▶️ Start",
                 command=self.start_monitor,
                 bg=self.colors['accent_green'], fg='white',
                 font=("Segoe UI", 9), relief='flat', padx=10, pady=3).pack(side=tk.LEFT, padx=2)

        tk.Button(monitor_controls, text="⏸️ Pause",
                 command=self.pause_monitor,
                 bg=self.colors['accent_orange'], fg='white',
                 font=("Segoe UI", 9), relief='flat', padx=10, pady=3).pack(side=tk.LEFT, padx=2)

        tk.Button(monitor_controls, text="🗑️ Clear",
                 command=self.clear_monitor,
                 bg=self.colors['accent_red'], fg='white',
                 font=("Segoe UI", 9), relief='flat', padx=10, pady=3).pack(side=tk.LEFT, padx=2)

        self.auto_scroll = tk.BooleanVar(value=True)
        tk.Checkbutton(monitor_controls, text="📜 Auto-scroll",
                      variable=self.auto_scroll,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_blue'],
                      font=("Segoe UI", 9)).pack(side=tk.RIGHT)

        # Live monitor display
        self.monitor_text = scrolledtext.ScrolledText(monitor_card, height=15,
                                                     font=("Consolas", 10),
                                                     bg='#0a0a0a', fg='#00ff00',
                                                     insertbackground='#00ff00')
        self.monitor_text.pack(fill=tk.BOTH, expand=True, pady=5)

        # Analytics Card
        analytics_card = self.create_card(self.middle_column, "📊 Campaign Analytics")

        # Stats grid
        stats_grid = tk.Frame(analytics_card, bg=self.colors['bg_secondary'])
        stats_grid.pack(fill=tk.X, pady=10)

        # Create stat boxes
        self.create_stat_box(stats_grid, "Messages Sent", "0", 0, 0)
        self.create_stat_box(stats_grid, "Success Rate", "0%", 0, 1)
        self.create_stat_box(stats_grid, "Groups Reached", "0", 1, 0)
        self.create_stat_box(stats_grid, "Active Campaigns", "0", 1, 1)

        # Progress bar
        progress_frame = tk.Frame(analytics_card, bg=self.colors['bg_secondary'])
        progress_frame.pack(fill=tk.X, pady=10)

        tk.Label(progress_frame, text="Campaign Progress:",
                font=("Segoe UI", 10, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W)

        self.progress_bar = ttk.Progressbar(progress_frame, length=400, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=5)

        self.progress_label = tk.Label(progress_frame, text="Ready to send",
                                      bg=self.colors['bg_secondary'],
                                      fg=self.colors['text_secondary'],
                                      font=("Segoe UI", 9))
        self.progress_label.pack(anchor=tk.W)

    def setup_right_column(self):
        """Setup right column - Discovery & Controls"""
        # Automation Control Card
        automation_card = self.create_card(self.right_column, "🔄 Automation Control")

        # Status display
        self.automation_status = tk.Label(automation_card,
                                         text="🔴 STOPPED",
                                         font=("Segoe UI", 16, "bold"),
                                         bg=self.colors['bg_secondary'],
                                         fg=self.colors['accent_red'])
        self.automation_status.pack(pady=10)

        # Control buttons
        control_frame = tk.Frame(automation_card, bg=self.colors['bg_secondary'])
        control_frame.pack(fill=tk.X, pady=10)

        self.start_automation_btn = tk.Button(control_frame,
                                             text="🟢 START",
                                             command=self.start_automation,
                                             bg=self.colors['accent_green'], fg='white',
                                             font=("Segoe UI", 12, "bold"),
                                             relief='flat', padx=20, pady=10)
        self.start_automation_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)

        self.stop_automation_btn = tk.Button(control_frame,
                                            text="🔴 STOP",
                                            command=self.stop_automation,
                                            bg=self.colors['accent_red'], fg='white',
                                            font=("Segoe UI", 12, "bold"),
                                            relief='flat', padx=20, pady=10)
        self.stop_automation_btn.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=2)

        # Automation settings
        settings_frame = tk.Frame(automation_card, bg=self.colors['bg_secondary'])
        settings_frame.pack(fill=tk.X, pady=10)

        self.continuous_mode = tk.BooleanVar(value=False)
        tk.Checkbutton(settings_frame, text="🔁 Run continuously (24/7)",
                      variable=self.continuous_mode,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_blue'],
                      font=("Segoe UI", 10)).pack(anchor=tk.W, pady=2)

        self.auto_comment = tk.BooleanVar(value=False)
        tk.Checkbutton(settings_frame, text="💬 Auto-comment on pinned messages",
                      variable=self.auto_comment,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_blue'],
                      font=("Segoe UI", 10)).pack(anchor=tk.W, pady=2)

        self.auto_discovery = tk.BooleanVar(value=False)
        tk.Checkbutton(settings_frame, text="🔍 Auto-discover new groups",
                      variable=self.auto_discovery,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_blue'],
                      font=("Segoe UI", 10)).pack(anchor=tk.W, pady=2)

        # Discovery Card
        discovery_card = self.create_card(self.right_column, "🔍 Group Discovery")

        # Discovery controls
        discovery_controls = tk.Frame(discovery_card, bg=self.colors['bg_secondary'])
        discovery_controls.pack(fill=tk.X, pady=5)

        self.start_discovery_btn = tk.Button(discovery_controls,
                                            text="🔍 START DISCOVERY",
                                            command=self.start_discovery,
                                            bg=self.colors['accent_blue'], fg='white',
                                            font=("Segoe UI", 11, "bold"),
                                            relief='flat', padx=15, pady=8)
        self.start_discovery_btn.pack(fill=tk.X, pady=2)

        self.stop_discovery_btn = tk.Button(discovery_controls,
                                           text="⏹️ STOP DISCOVERY",
                                           command=self.stop_discovery,
                                           bg=self.colors['accent_red'], fg='white',
                                           font=("Segoe UI", 11, "bold"),
                                           relief='flat', padx=15, pady=8)
        self.stop_discovery_btn.pack(fill=tk.X, pady=2)

        # Discovery status
        self.discovery_status = tk.Label(discovery_card,
                                        text="🔴 Discovery: STOPPED",
                                        bg=self.colors['bg_secondary'],
                                        fg=self.colors['accent_red'],
                                        font=("Segoe UI", 11, "bold"))
        self.discovery_status.pack(pady=5)

        # Keywords
        tk.Label(discovery_card, text="🔑 Search Keywords:",
                font=("Segoe UI", 10, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W, pady=(10,5))

        self.keywords_text = scrolledtext.ScrolledText(discovery_card, height=3,
                                                      font=("Segoe UI", 10),
                                                      bg='#2a2a2a', fg='white',
                                                      insertbackground='white')
        self.keywords_text.pack(fill=tk.X, pady=5)
        self.keywords_text.insert(1.0, "solana, pump, shill, gem, crypto, token, defi, trading")

        # Discovery settings
        disc_settings = tk.Frame(discovery_card, bg=self.colors['bg_secondary'])
        disc_settings.pack(fill=tk.X, pady=10)

        self.auto_join_discovered = tk.BooleanVar(value=False)
        tk.Checkbutton(disc_settings, text="🤝 Auto-join discovered groups",
                      variable=self.auto_join_discovered,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_blue'],
                      font=("Segoe UI", 10)).pack(anchor=tk.W, pady=2)

        # Discovery stats
        self.disc_stats_frame = tk.Frame(discovery_card, bg=self.colors['bg_secondary'])
        self.disc_stats_frame.pack(fill=tk.X, pady=10)

        self.disc_stats_found = tk.Label(self.disc_stats_frame, text="Groups Found: 0",
                                        bg=self.colors['bg_secondary'],
                                        fg=self.colors['text_secondary'],
                                        font=("Segoe UI", 9))
        self.disc_stats_found.pack(anchor=tk.W)

        self.disc_stats_joined = tk.Label(self.disc_stats_frame, text="Groups Joined: 0",
                                         bg=self.colors['bg_secondary'],
                                         fg=self.colors['text_secondary'],
                                         font=("Segoe UI", 9))
        self.disc_stats_joined.pack(anchor=tk.W)

    def create_stat_box(self, parent, title, value, row, col):
        """Create a statistics box"""
        stat_frame = tk.Frame(parent, bg=self.colors['bg_accent'], relief='flat', bd=1)
        stat_frame.grid(row=row, column=col, padx=5, pady=5, sticky='ew')
        parent.grid_columnconfigure(col, weight=1)

        value_label = tk.Label(stat_frame, text=value,
                              font=("Segoe UI", 16, "bold"),
                              bg=self.colors['bg_accent'],
                              fg=self.colors['text_primary'])
        value_label.pack(pady=(10,0))

        title_label = tk.Label(stat_frame, text=title,
                              font=("Segoe UI", 9),
                              bg=self.colors['bg_accent'],
                              fg=self.colors['text_secondary'])
        title_label.pack(pady=(0,10))

        # Store reference for updating
        setattr(self, f"stat_{title.lower().replace(' ', '_')}", value_label)

    def toggle_automation(self):
        """Toggle automation on/off"""
        if self.automation_running:
            self.stop_automation()
        else:
            self.start_automation()

        # Title
        title_label = tk.Label(main_frame, text="🚀 TELEGRAM DM BOT",
                              font=("Arial", 20, "bold"),
                              bg='#2b2b2b', fg='#ffffff')
        title_label.pack(pady=10)

        subtitle_label = tk.Label(main_frame, text="Bulk Messaging Tool - Send to 1000+ Groups",
                                 font=("Arial", 12),
                                 bg='#2b2b2b', fg='#cccccc')
        subtitle_label.pack(pady=5)

        # Connection frame
        conn_frame = ttk.LabelFrame(main_frame, text="Connection Status", padding=10)
        conn_frame.pack(fill=tk.X, pady=10)

        self.status_label = tk.Label(conn_frame, text="❌ Not Connected",
                                    font=("Arial", 10), fg='red')
        self.status_label.pack(side=tk.LEFT)

        btn_frame = tk.Frame(conn_frame)
        btn_frame.pack(side=tk.RIGHT)

        self.test_btn = tk.Button(btn_frame, text="Test Connection",
                                 command=self.test_connection,
                                 bg='#FF9800', fg='white', font=("Arial", 9))
        self.test_btn.pack(side=tk.LEFT, padx=5)

        self.connect_btn = tk.Button(btn_frame, text="Connect to Telegram",
                                    command=self.connect_telegram,
                                    bg='#4CAF50', fg='white', font=("Arial", 10))
        self.connect_btn.pack(side=tk.LEFT)

        # Login container frame
        self.login_container = ttk.LabelFrame(main_frame, text="🔐 Login to Telegram", padding=15)
        self.login_container.pack(fill=tk.X, pady=10)

        # Phone number frame (initially hidden)
        self.phone_frame = ttk.Frame(self.login_container)

        tk.Label(self.phone_frame, text="📱 Step 1: Enter Phone Number",
                font=("Arial", 11, "bold")).pack(anchor=tk.W)
        tk.Label(self.phone_frame, text="Examples: ****** 863 3617 or +1234567890",
                font=("Arial", 9), fg='gray').pack(anchor=tk.W, pady=(0,5))

        phone_input_frame = tk.Frame(self.phone_frame)
        phone_input_frame.pack(fill=tk.X, pady=5)

        self.phone_entry = tk.Entry(phone_input_frame, font=("Arial", 11), width=20)
        self.phone_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.phone_entry.insert(0, "+1 ")  # Pre-fill with US country code

        self.send_code_btn = tk.Button(phone_input_frame, text="Send Code",
                                      command=self.send_verification_code,
                                      bg='#2196F3', fg='white', font=("Arial", 10))
        self.send_code_btn.pack(side=tk.RIGHT, padx=(10,0))

        # Code entry frame (initially hidden)
        self.code_frame = ttk.Frame(self.login_container)

        tk.Label(self.code_frame, text="📨 Step 2: Enter Verification Code",
                font=("Arial", 11, "bold")).pack(anchor=tk.W, pady=(10,0))
        self.code_status_label = tk.Label(self.code_frame, text="✅ Code sent to your phone",
                                         fg='green', font=("Arial", 9))
        self.code_status_label.pack(anchor=tk.W, pady=(0,5))

        code_input_frame = tk.Frame(self.code_frame)
        code_input_frame.pack(fill=tk.X, pady=5)

        self.code_entry = tk.Entry(code_input_frame, font=("Arial", 11), width=15)
        self.code_entry.pack(side=tk.LEFT)

        self.login_btn = tk.Button(code_input_frame, text="Complete Login",
                                  command=self.complete_login,
                                  bg='#4CAF50', fg='white', font=("Arial", 10))
        self.login_btn.pack(side=tk.RIGHT, padx=(10,0))

        # Two-step verification frame (initially hidden)
        self.password_frame = ttk.Frame(self.login_container)

        tk.Label(self.password_frame, text="🔐 Step 3: Two-Step Verification",
                font=("Arial", 11, "bold")).pack(anchor=tk.W, pady=(10,0))
        self.password_status_label = tk.Label(self.password_frame, text="🔒 2FA password required",
                                             fg='orange', font=("Arial", 9))
        self.password_status_label.pack(anchor=tk.W, pady=(0,5))

        password_input_frame = tk.Frame(self.password_frame)
        password_input_frame.pack(fill=tk.X, pady=5)

        self.password_entry = tk.Entry(password_input_frame, font=("Arial", 11), width=20, show="*")
        self.password_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.password_btn = tk.Button(password_input_frame, text="Submit Password",
                                     command=self.submit_password,
                                     bg='#FF5722', fg='white', font=("Arial", 10))
        self.password_btn.pack(side=tk.RIGHT, padx=(10,0))

        # Main content frame (initially hidden)
        self.main_content = ttk.Frame(main_frame)

        # Notebook for tabs
        self.notebook = ttk.Notebook(self.main_content)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Bulk messaging tab
        self.bulk_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.bulk_frame, text="📤 Bulk Messaging")

        # Group Manager tab
        self.group_manager_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.group_manager_frame, text="📋 Group Manager")

        # Campaign Analytics tab
        self.analytics_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.analytics_frame, text="📊 Analytics")

        # Message Scheduler tab
        self.scheduler_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.scheduler_frame, text="⏰ Scheduler")

        # Automation Control tab
        self.automation_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.automation_frame, text="🔄 Automation")

        # Live Monitor tab
        self.monitor_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.monitor_frame, text="📺 Monitor")

        # Group Discovery tab
        self.discovery_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.discovery_frame, text="🔍 Discovery")

        # Groups selection
        groups_label_frame = ttk.LabelFrame(self.bulk_frame, text="Select Groups", padding=10)
        groups_label_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Groups listbox with scrollbar
        list_frame = tk.Frame(groups_label_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        scrollbar = tk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.groups_listbox = tk.Listbox(list_frame, selectmode=tk.MULTIPLE,
                                        yscrollcommand=scrollbar.set,
                                        font=("Arial", 10))
        self.groups_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.groups_listbox.yview)

        # Selection buttons
        btn_frame = tk.Frame(groups_label_frame)
        btn_frame.pack(fill=tk.X, pady=5)

        tk.Button(btn_frame, text="Select All", command=self.select_all_groups,
                 bg='#FF9800', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="Clear Selection", command=self.clear_selection,
                 bg='#f44336', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="Refresh Groups", command=self.refresh_groups,
                 bg='#9C27B0', fg='white').pack(side=tk.LEFT, padx=5)

        # Message composition
        msg_frame = ttk.LabelFrame(self.bulk_frame, text="📝 Compose Message", padding=10)
        msg_frame.pack(fill=tk.X, pady=5)

        # Message type selection
        msg_type_frame = tk.Frame(msg_frame)
        msg_type_frame.pack(fill=tk.X, pady=(0,10))

        tk.Label(msg_type_frame, text="Message Type:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)

        self.msg_type_var = tk.StringVar(value="text")
        msg_types = [("📄 Text Only", "text"), ("🖼️ Photo + Text", "photo"), ("🔗 Link Preview", "link")]

        for text, value in msg_types:
            tk.Radiobutton(msg_type_frame, text=text, variable=self.msg_type_var,
                          value=value, command=self.on_message_type_change).pack(side=tk.LEFT, padx=10)

        # Photo selection frame (initially hidden)
        self.photo_frame = tk.Frame(msg_frame)

        photo_btn_frame = tk.Frame(self.photo_frame)
        photo_btn_frame.pack(fill=tk.X, pady=5)

        self.photo_btn = tk.Button(photo_btn_frame, text="📁 Select Photo",
                                  command=self.select_photo,
                                  bg='#FF9800', fg='white', font=("Arial", 10))
        self.photo_btn.pack(side=tk.LEFT)

        self.photo_label = tk.Label(photo_btn_frame, text="No photo selected",
                                   font=("Arial", 9), fg='gray')
        self.photo_label.pack(side=tk.LEFT, padx=(10,0))

        self.clear_photo_btn = tk.Button(photo_btn_frame, text="❌ Clear",
                                        command=self.clear_photo,
                                        bg='#f44336', fg='white', font=("Arial", 9))
        self.clear_photo_btn.pack(side=tk.RIGHT)

        # Message text area
        text_label_frame = tk.Frame(msg_frame)
        text_label_frame.pack(fill=tk.X, pady=(5,0))

        tk.Label(text_label_frame, text="Message Text:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.char_count_label = tk.Label(text_label_frame, text="0/4096 characters",
                                        font=("Arial", 9), fg='gray')
        self.char_count_label.pack(side=tk.RIGHT)

        self.message_text = scrolledtext.ScrolledText(msg_frame, height=8,
                                                     font=("Arial", 11))
        self.message_text.pack(fill=tk.X, pady=5)
        self.message_text.bind('<KeyRelease>', self.update_char_count)

        # Quick templates
        template_frame = tk.Frame(msg_frame)
        template_frame.pack(fill=tk.X, pady=5)

        tk.Label(template_frame, text="Quick Templates:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)

        templates = [
            ("🚀 Crypto", "🚀 New Token Alert! 🚀\n\n💎 Token: [TOKEN_NAME]\n📈 Price: $[PRICE]\n🔗 Website: [WEBSITE]\n\n#crypto #token #investment"),
            ("💰 Trading", "💰 Trading Signal Alert! 💰\n\n📊 Pair: [PAIR]\n📈 Entry: [ENTRY_PRICE]\n🎯 Target: [TARGET]\n🛑 Stop Loss: [STOP_LOSS]\n\n#trading #signal"),
            ("🎯 Promo", "🎯 Special Promotion! 🎯\n\n🔥 [OFFER_TITLE]\n💸 Discount: [DISCOUNT]%\n⏰ Limited Time Only!\n🔗 Link: [WEBSITE]\n\n#promo #discount #sale")
        ]

        for name, template in templates:
            btn = tk.Button(template_frame, text=name,
                           command=lambda t=template: self.insert_template(t),
                           bg='#9C27B0', fg='white', font=("Arial", 9))
            btn.pack(side=tk.LEFT, padx=5)

        # Message preview
        self.preview_frame = ttk.LabelFrame(msg_frame, text="📱 Message Preview", padding=5)

        self.preview_text = tk.Text(self.preview_frame, height=4, font=("Arial", 10),
                                   state='disabled', bg='#f0f0f0')
        self.preview_text.pack(fill=tk.X)

        # Initialize
        self.selected_photo_path = None
        self.group_categories = {}
        self.campaign_history = []
        self.scheduled_messages = []
        self.automation_running = False
        self.automation_thread = None
        self.monitor_data = []
        self.discovery_running = False
        self.discovery_thread = None
        self.discovered_groups = []
        self.discovery_keywords = ["solana", "pump", "shill", "gem", "crypto", "token", "defi", "trading"]

        # Rate limiting
        rate_frame = ttk.LabelFrame(self.bulk_frame, text="Rate Limiting", padding=10)
        rate_frame.pack(fill=tk.X, pady=5)

        self.rate_var = tk.StringVar(value="1")
        rate_options = [("Fast (0.5s)", "0.5"), ("Normal (1s)", "1"),
                       ("Safe (2s)", "2"), ("Custom", "custom")]

        for text, value in rate_options:
            tk.Radiobutton(rate_frame, text=text, variable=self.rate_var,
                          value=value).pack(side=tk.LEFT, padx=10)

        self.custom_delay = tk.Entry(rate_frame, width=10)
        self.custom_delay.pack(side=tk.RIGHT)
        tk.Label(rate_frame, text="Custom delay (seconds):").pack(side=tk.RIGHT, padx=5)

        # Send button
        self.send_btn = tk.Button(self.bulk_frame, text="🚀 SEND BULK MESSAGES",
                                 command=self.send_bulk_messages,
                                 bg='#4CAF50', fg='white',
                                 font=("Arial", 14, "bold"), height=2)
        self.send_btn.pack(fill=tk.X, pady=10)

        # Progress frame
        self.progress_frame = ttk.LabelFrame(self.bulk_frame, text="Progress", padding=10)

        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=5)

        self.progress_label = tk.Label(self.progress_frame, text="Ready to send messages")
        self.progress_label.pack()

        # Statistics tab
        self.stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.stats_frame, text="📊 Statistics")

        self.stats_text = scrolledtext.ScrolledText(self.stats_frame,
                                                   font=("Courier", 11))
        self.stats_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Log tab
        self.log_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.log_frame, text="📝 Logs")

        self.log_text = scrolledtext.ScrolledText(self.log_frame,
                                                 font=("Courier", 10))
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Setup additional tabs
        self.setup_group_manager()
        self.setup_analytics()
        self.setup_scheduler()
        self.setup_automation()
        self.setup_monitor()
        self.setup_discovery()
        

    
    def log_message(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n")
        self.log_text.see(tk.END)
        self.root.update()

    def test_connection(self):
        """Test internet connection and API credentials"""
        self.test_btn.config(state='disabled', text='Testing...')
        self.log_message("Testing connection...")

        thread = threading.Thread(target=self.test_connection_thread)
        thread.daemon = True
        thread.start()

    def test_connection_thread(self):
        """Test connection in thread"""
        try:
            import socket
            import urllib.request

            # Test internet connection
            self.log_message("Testing internet connection...")
            try:
                socket.create_connection(("8.8.8.8", 53), timeout=5)
                self.log_message("✅ Internet connection: OK")
            except:
                self.log_message("❌ Internet connection: FAILED")
                self.root.after(0, lambda: messagebox.showerror("Connection Test",
                    "No internet connection detected. Please check your network."))
                self.root.after(0, lambda: self.test_btn.config(state='normal', text='Test Connection'))
                return

            # Test Telegram servers
            self.log_message("Testing Telegram servers...")
            try:
                socket.create_connection(("149.154.167.50", 443), timeout=10)
                self.log_message("✅ Telegram servers: Reachable")
            except:
                self.log_message("❌ Telegram servers: Not reachable")
                self.root.after(0, lambda: messagebox.showwarning("Connection Test",
                    "Cannot reach Telegram servers. This might be due to firewall or network restrictions."))

            # Test API credentials format
            self.log_message("Testing API credentials format...")
            if self.api_id and isinstance(self.api_id, int) and self.api_id > 0:
                self.log_message("✅ API ID format: Valid")
            else:
                self.log_message("❌ API ID format: Invalid")

            if self.api_hash and isinstance(self.api_hash, str) and len(self.api_hash) == 32:
                self.log_message("✅ API Hash format: Valid")
            else:
                self.log_message("❌ API Hash format: Invalid")

            self.log_message("Connection test completed. Check logs for details.")
            self.root.after(0, lambda: messagebox.showinfo("Connection Test",
                "Connection test completed. Check the logs tab for detailed results."))

        except Exception as e:
            self.log_message(f"Test error: {str(e)}")
        finally:
            self.root.after(0, lambda: self.test_btn.config(state='normal', text='Test Connection'))

    def connect_telegram(self):
        """Start connection process"""
        self.connect_btn.config(state='disabled', text='Connecting...')
        self.log_message("Starting connection to Telegram...")

        # Run connection in thread
        thread = threading.Thread(target=self.connect_thread)
        thread.daemon = True
        thread.start()

    def connect_thread(self):
        """Connection thread"""
        try:
            # Check if event loop is ready
            if not self.event_loop:
                self.log_message("❌ Event loop not ready, retrying...")
                import time
                time.sleep(1)
                if not self.event_loop:
                    raise Exception("Event loop failed to start")

            # Use the dedicated event loop
            future = asyncio.run_coroutine_threadsafe(self.connect_async(), self.event_loop)
            future.result(timeout=30)  # Wait for result with timeout
        except Exception as e:
            self.log_message(f"Connection error: {str(e)}")
            self.root.after(0, lambda: self.connect_btn.config(state='normal', text='Connect to Telegram'))

    async def connect_async(self):
        """Connect to Telegram"""
        try:
            self.log_message(f"Using API ID: {self.api_id}")
            self.log_message(f"Using API Hash: {self.api_hash[:10]}...")

            self.client = TelegramClient(self.session_name, self.api_id, self.api_hash)
            self.log_message("Connecting to Telegram servers...")

            # Connect to Telegram with timeout
            await asyncio.wait_for(self.client.connect(), timeout=30)
            self.log_message("Connected to Telegram servers successfully!")

            # Check if already logged in
            if await self.client.is_user_authorized():
                me = await self.client.get_me()
                self.log_message(f"Already logged in as: {me.first_name}")
                self.root.after(0, self.on_login_success)
                return True
            else:
                self.log_message("Connected! Ready for phone number login.")
                self.root.after(0, self.show_phone_input)
                return True

        except asyncio.TimeoutError:
            self.log_message("Connection timeout! Check your internet connection.")
            self.root.after(0, lambda: messagebox.showerror("Connection Error",
                "Connection timeout! Please check your internet connection and try again."))
            self.root.after(0, lambda: self.connect_btn.config(state='normal', text='Connect to Telegram'))
            return False
        except Exception as e:
            error_msg = str(e)
            self.log_message(f"Connection failed: {error_msg}")

            # Specific error messages
            if "api_id" in error_msg.lower() or "api_hash" in error_msg.lower():
                self.root.after(0, lambda: messagebox.showerror("API Error",
                    "Invalid API credentials. Please check your API ID and Hash."))
            elif "network" in error_msg.lower() or "connection" in error_msg.lower():
                self.root.after(0, lambda: messagebox.showerror("Network Error",
                    "Network connection failed. Please check your internet connection."))
            else:
                self.root.after(0, lambda: messagebox.showerror("Connection Error",
                    f"Failed to connect: {error_msg}"))

            self.root.after(0, lambda: self.connect_btn.config(state='normal', text='Connect to Telegram'))
            return False

    def show_phone_input(self):
        """Show phone number input"""
        self.connect_btn.config(state='disabled', text='Connected ✅')
        self.login_container.pack(fill=tk.X, pady=10)
        self.phone_frame.pack(fill=tk.X, pady=5)
        self.update_scroll_region()

    def send_verification_code(self):
        """Send verification code to phone"""
        phone_raw = self.phone_entry.get().strip()

        if not phone_raw:
            messagebox.showerror("Error", "Please enter your phone number")
            return

        # Clean up phone number - remove spaces, dashes, parentheses
        phone = phone_raw.replace(' ', '').replace('-', '').replace('(', '').replace(')', '').replace('.', '')

        self.log_message(f"Original input: {phone_raw}")
        self.log_message(f"Cleaned number: {phone}")

        # Validate phone number format
        if not phone.startswith('+'):
            messagebox.showerror("Error", "Phone number must start with + and include country code\n\nExamples:\n****** 863 3617 (US)\n+44 7123 456789 (UK)\n+33 1 23 45 67 89 (France)")
            return

        # Remove + and check if rest are digits
        phone_digits = phone[1:]
        if not phone_digits.isdigit():
            messagebox.showerror("Error", f"Phone number can only contain +, digits, spaces, and dashes\n\nYour cleaned number: {phone}\nPlease check for invalid characters")
            return

        if len(phone_digits) < 7 or len(phone_digits) > 15:
            messagebox.showerror("Error", f"Phone number should have 7-15 digits after country code\n\nYour number has {len(phone_digits)} digits: {phone}")
            return

        self.log_message(f"Phone number validation passed: {phone}")

        # Update the entry field with cleaned number for user reference
        self.phone_entry.delete(0, tk.END)
        self.phone_entry.insert(0, phone)

        self.send_code_btn.config(state='disabled', text='Sending code...')
        self.log_message(f"Sending verification code to {phone}")

        # Run in thread
        thread = threading.Thread(target=self.send_code_thread, args=(phone,))
        thread.daemon = True
        thread.start()

    def send_code_thread(self, phone):
        """Send code in thread"""
        try:
            # Check if event loop is ready
            if not self.event_loop:
                raise Exception("Event loop not ready")

            # Use the dedicated event loop
            future = asyncio.run_coroutine_threadsafe(self.send_code_async(phone), self.event_loop)
            future.result(timeout=30)  # Wait for result with timeout
        except Exception as e:
            self.log_message(f"Error sending code: {str(e)}")
            self.root.after(0, lambda: self.send_code_btn.config(state='normal', text='Send Verification Code'))

    async def send_code_async(self, phone):
        """Send verification code async"""
        try:
            self.log_message(f"Requesting verification code for {phone}")

            # Send code request with timeout
            result = await asyncio.wait_for(self.client.send_code_request(phone), timeout=30)

            self.log_message("Verification code request sent successfully!")
            self.log_message(f"Code type: {result.type}")

            if hasattr(result, 'next_type') and result.next_type:
                self.log_message(f"Alternative method available: {result.next_type}")

            self.log_message("Check your phone for the verification code (SMS or Telegram app)")
            self.root.after(0, self.show_code_input)

        except asyncio.TimeoutError:
            self.log_message("Timeout while sending verification code")
            self.root.after(0, lambda: messagebox.showerror("Timeout Error",
                "Timeout while sending verification code. Please try again."))
            self.root.after(0, lambda: self.send_code_btn.config(state='normal', text='Send Verification Code'))
        except Exception as e:
            error_msg = str(e)
            self.log_message(f"Failed to send code: {error_msg}")

            # Specific error messages for common issues
            if "phone number invalid" in error_msg.lower():
                self.root.after(0, lambda: messagebox.showerror("Invalid Phone Number",
                    "Invalid phone number format. Please use international format with country code (e.g., +1234567890)"))
            elif "phone number banned" in error_msg.lower():
                self.root.after(0, lambda: messagebox.showerror("Phone Banned",
                    "This phone number is banned from Telegram. Please use a different number."))
            elif "flood" in error_msg.lower():
                self.root.after(0, lambda: messagebox.showerror("Rate Limited",
                    "Too many requests. Please wait a few minutes before trying again."))
            else:
                self.root.after(0, lambda: messagebox.showerror("Error",
                    f"Failed to send verification code: {error_msg}"))

            self.root.after(0, lambda: self.send_code_btn.config(state='normal', text='Send Verification Code'))

    def show_code_input(self):
        """Show code input after code is sent"""
        self.send_code_btn.config(state='disabled', text='Code Sent ✅')
        self.code_frame.pack(fill=tk.X, pady=5)
        self.code_entry.focus()
        self.update_scroll_region()

    def complete_login(self):
        """Complete login with verification code"""
        phone = self.phone_entry.get().strip()
        code = self.code_entry.get().strip()

        if not code:
            messagebox.showerror("Error", "Please enter the verification code")
            return

        self.login_btn.config(state='disabled', text='Logging in...')
        self.log_message(f"Completing login with verification code...")

        # Run in thread
        thread = threading.Thread(target=self.complete_login_thread, args=(phone, code))
        thread.daemon = True
        thread.start()

    def complete_login_thread(self, phone, code):
        """Complete login in thread"""
        try:
            # Use the dedicated event loop
            future = asyncio.run_coroutine_threadsafe(self.complete_login_async(phone, code), self.event_loop)
            future.result(timeout=30)  # Wait for result with timeout
        except Exception as e:
            self.log_message(f"Login error: {str(e)}")
            self.root.after(0, lambda: self.login_btn.config(state='normal', text='Complete Login'))

    async def complete_login_async(self, phone, code):
        """Complete login async"""
        try:
            await self.client.sign_in(phone, code)
            me = await self.client.get_me()
            self.log_message(f"Successfully logged in as: {me.first_name}")
            self.root.after(0, self.on_login_success)

        except Exception as e:
            error_str = str(e).lower()
            self.log_message(f"Login attempt result: {str(e)}")

            if "two-step" in error_str or "2fa" in error_str or "password" in error_str:
                self.log_message("Two-step verification required")
                self.root.after(0, self.show_password_input)
            elif "phone code invalid" in error_str:
                messagebox.showerror("Error", "Invalid verification code. Please try again.")
                self.root.after(0, lambda: self.login_btn.config(state='normal', text='Complete Login'))
            elif "phone code expired" in error_str:
                messagebox.showerror("Error", "Verification code expired. Please request a new one.")
                self.root.after(0, self.reset_to_phone_input)
            else:
                messagebox.showerror("Error", f"Login failed: {str(e)}")
                self.root.after(0, lambda: self.login_btn.config(state='normal', text='Complete Login'))

    def update_scroll_region(self):
        """Update the scroll region after adding/removing widgets"""
        self.canvas.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def reset_to_phone_input(self):
        """Reset to phone input if code expired"""
        self.code_frame.pack_forget()
        self.password_frame.pack_forget()
        self.send_code_btn.config(state='normal', text='Send Code')
        self.code_entry.delete(0, tk.END)
        self.update_scroll_region()

    def show_password_input(self):
        """Show password input for two-step verification"""
        self.login_btn.config(state='disabled', text='2FA Required')
        self.password_frame.pack(fill=tk.X, pady=5)
        self.password_entry.focus()
        self.update_scroll_region()
        # Scroll to bottom to show password input
        self.canvas.update_idletasks()
        self.canvas.yview_moveto(1.0)

    def submit_password(self):
        """Submit two-step verification password"""
        phone = self.phone_entry.get().strip()
        password = self.password_entry.get().strip()

        if not password:
            messagebox.showerror("Error", "Please enter your two-step verification password")
            return

        self.password_btn.config(state='disabled', text='Verifying...')
        self.log_message("Submitting two-step verification password...")

        # Run in thread
        thread = threading.Thread(target=self.submit_password_thread, args=(phone, password))
        thread.daemon = True
        thread.start()

    def submit_password_thread(self, phone, password):
        """Submit password in thread"""
        try:
            # Check if event loop is ready
            if not self.event_loop:
                raise Exception("Event loop not ready")

            # Use the dedicated event loop
            future = asyncio.run_coroutine_threadsafe(self.submit_password_async(phone, password), self.event_loop)
            future.result(timeout=30)  # Wait for result with timeout
        except Exception as e:
            self.log_message(f"Password error: {str(e)}")
            self.root.after(0, lambda: self.password_btn.config(state='normal', text='Submit Password'))

    async def submit_password_async(self, phone, password):
        """Submit password async"""
        try:
            await self.client.sign_in(password=password)
            me = await self.client.get_me()
            self.log_message(f"Successfully logged in with 2FA as: {me.first_name}")
            self.root.after(0, self.on_login_success)

        except Exception as e:
            error_str = str(e).lower()
            self.log_message(f"2FA login failed: {str(e)}")

            if "password invalid" in error_str or "invalid password" in error_str:
                messagebox.showerror("Error", "Invalid two-step verification password. Please try again.")
            elif "too many" in error_str or "flood" in error_str:
                messagebox.showerror("Error", "Too many attempts. Please wait before trying again.")
            else:
                messagebox.showerror("Error", f"Two-step verification failed: {str(e)}")

            self.root.after(0, lambda: self.password_btn.config(state='normal', text='Submit Password'))

    def on_login_success(self):
        """Called when login is successful"""
        self.is_connected = True
        self.status_label.config(text="✅ Connected & Logged In", fg='green')
        self.login_container.pack_forget()
        self.main_content.pack(fill=tk.BOTH, expand=True, pady=10)
        self.update_scroll_region()
        self.refresh_groups()
    
    def refresh_groups(self):
        """Refresh groups list"""
        if not self.is_connected:
            return

        self.log_message("Loading groups...")
        thread = threading.Thread(target=self.refresh_groups_thread)
        thread.daemon = True
        thread.start()

    def refresh_groups_thread(self):
        """Refresh groups in thread"""
        try:
            # Use the dedicated event loop
            future = asyncio.run_coroutine_threadsafe(self.get_contacts_async(), self.event_loop)
            future.result(timeout=30)  # Wait for result with timeout
        except Exception as e:
            self.log_message(f"Error loading groups: {str(e)}")

    async def get_contacts_async(self):
        """Get all contacts and chats"""
        self.log_message("Loading your contacts and chats...")
        contacts = []
        groups = []

        try:
            async for dialog in self.client.iter_dialogs():
                contact_type = "Unknown"
                if isinstance(dialog.entity, User):
                    if not dialog.entity.bot:
                        contact_type = "User"
                    else:
                        contact_type = "Bot"
                elif isinstance(dialog.entity, Chat):
                    contact_type = "Group"
                elif isinstance(dialog.entity, Channel):
                    contact_type = "Channel" if dialog.entity.broadcast else "Supergroup"

                contact = {
                    'id': dialog.entity.id,
                    'name': dialog.name or "Unknown",
                    'type': contact_type,
                    'entity': dialog.entity
                }

                contacts.append(contact)

                # Add to groups list if it's a group
                if contact_type in ['Group', 'Supergroup']:
                    groups.append(contact)

            self.contacts = contacts
            self.groups = groups

            self.log_message(f"Loaded {len(contacts)} contacts ({len(groups)} groups)")

            # Update GUI
            self.root.after(0, self.update_groups_list)
            self.root.after(0, self.update_statistics)

        except Exception as e:
            self.log_message(f"Failed to load contacts: {str(e)}")

    def update_groups_list(self):
        """Update groups listbox"""
        self.groups_listbox.delete(0, tk.END)
        for group in self.groups:
            self.groups_listbox.insert(tk.END, f"{group['name']} ({group['type']})")

    def update_statistics(self):
        """Update statistics tab"""
        users = [c for c in self.contacts if c['type'] == 'User']
        groups = [c for c in self.contacts if c['type'] in ['Group', 'Supergroup']]
        channels = [c for c in self.contacts if c['type'] == 'Channel']
        bots = [c for c in self.contacts if c['type'] == 'Bot']

        stats = f"""📊 CONTACT STATISTICS
{'='*50}
👥 Users: {len(users)}
👨‍👩‍👧‍👦 Groups: {len(groups)}
📢 Channels: {len(channels)}
🤖 Bots: {len(bots)}
📱 Total: {len(self.contacts)}
{'='*50}

🎯 BULK MESSAGING POTENTIAL:
• You can send to {len(groups)} groups at once
• Estimated time (1s delay): {len(groups)} seconds
• Estimated time (2s delay): {len(groups)*2} seconds
"""

        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats)

    def select_all_groups(self):
        """Select all groups"""
        self.groups_listbox.select_set(0, tk.END)

    def clear_selection(self):
        """Clear group selection"""
        self.groups_listbox.selection_clear(0, tk.END)

    def on_message_type_change(self):
        """Handle message type change"""
        msg_type = self.msg_type_var.get()

        if msg_type == "photo":
            self.photo_frame.pack(fill=tk.X, pady=5)
            self.preview_frame.pack(fill=tk.X, pady=5)
        else:
            self.photo_frame.pack_forget()
            if msg_type == "text":
                self.preview_frame.pack_forget()
            else:  # link
                self.preview_frame.pack(fill=tk.X, pady=5)

        self.update_message_preview()

    def select_photo(self):
        """Select photo file"""
        from tkinter import filedialog

        filetypes = [
            ("Image files", "*.jpg *.jpeg *.png *.gif *.bmp *.webp"),
            ("JPEG files", "*.jpg *.jpeg"),
            ("PNG files", "*.png"),
            ("All files", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title="Select Photo",
            filetypes=filetypes
        )

        if filename:
            self.selected_photo_path = filename
            # Show just the filename, not full path
            import os
            photo_name = os.path.basename(filename)
            self.photo_label.config(text=f"📷 {photo_name}", fg='green')
            self.log_message(f"Photo selected: {photo_name}")
            self.update_message_preview()

    def clear_photo(self):
        """Clear selected photo"""
        self.selected_photo_path = None
        self.photo_label.config(text="No photo selected", fg='gray')
        self.log_message("Photo cleared")
        self.update_message_preview()

    def insert_template(self, template):
        """Insert template into message"""
        self.message_text.delete(1.0, tk.END)
        self.message_text.insert(1.0, template)
        self.update_char_count()
        self.update_message_preview()

    def update_char_count(self, event=None):
        """Update character count"""
        content = self.message_text.get(1.0, tk.END).strip()
        char_count = len(content)
        max_chars = 4096

        color = 'red' if char_count > max_chars else 'gray'
        self.char_count_label.config(text=f"{char_count}/{max_chars} characters", fg=color)

        self.update_message_preview()

    def update_message_preview(self):
        """Update message preview"""
        if not hasattr(self, 'preview_text'):
            return

        msg_type = self.msg_type_var.get()
        content = self.message_text.get(1.0, tk.END).strip()

        self.preview_text.config(state='normal')
        self.preview_text.delete(1.0, tk.END)

        preview = ""

        if msg_type == "photo" and self.selected_photo_path:
            import os
            photo_name = os.path.basename(self.selected_photo_path)
            preview += f"🖼️ Photo: {photo_name}\n\n"

        if content:
            # Process links for preview
            if msg_type == "link" or "http" in content.lower():
                import re
                urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', content)
                if urls:
                    preview += "🔗 Links detected:\n"
                    for url in urls[:3]:  # Show first 3 links
                        preview += f"  • {url}\n"
                    preview += "\n"

            preview += content

        if not preview:
            preview = "Type your message above to see preview..."

        self.preview_text.insert(1.0, preview)
        self.preview_text.config(state='disabled')

    def setup_group_manager(self):
        """Setup Group Manager tab"""
        # Categories section
        cat_frame = ttk.LabelFrame(self.group_manager_frame, text="📂 Group Categories", padding=10)
        cat_frame.pack(fill=tk.X, padx=10, pady=5)

        cat_btn_frame = tk.Frame(cat_frame)
        cat_btn_frame.pack(fill=tk.X, pady=5)

        tk.Button(cat_btn_frame, text="➕ Add Category", command=self.add_category,
                 bg='#4CAF50', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(cat_btn_frame, text="📝 Edit Category", command=self.edit_category,
                 bg='#FF9800', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(cat_btn_frame, text="🗑️ Delete Category", command=self.delete_category,
                 bg='#f44336', fg='white').pack(side=tk.LEFT, padx=5)

        # Category list
        self.category_listbox = tk.Listbox(cat_frame, height=6)
        self.category_listbox.pack(fill=tk.X, pady=5)
        self.category_listbox.bind('<<ListboxSelect>>', self.on_category_select)

        # Group management section
        group_frame = ttk.LabelFrame(self.group_manager_frame, text="👥 Group Management", padding=10)
        group_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        group_btn_frame = tk.Frame(group_frame)
        group_btn_frame.pack(fill=tk.X, pady=5)

        tk.Button(group_btn_frame, text="📥 Import Groups (CSV)", command=self.import_groups,
                 bg='#2196F3', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(group_btn_frame, text="📤 Export Groups (CSV)", command=self.export_groups,
                 bg='#9C27B0', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(group_btn_frame, text="🔄 Sync with Telegram", command=self.sync_groups,
                 bg='#00BCD4', fg='white').pack(side=tk.LEFT, padx=5)

        # Group list with categories
        list_frame = tk.Frame(group_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Group tree view
        columns = ('Name', 'Type', 'Category', 'Status', 'Last Used')
        self.group_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.group_tree.heading(col, text=col)
            self.group_tree.column(col, width=120)

        group_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.group_tree.yview)
        self.group_tree.configure(yscrollcommand=group_scrollbar.set)

        self.group_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        group_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Group actions
        action_frame = tk.Frame(group_frame)
        action_frame.pack(fill=tk.X, pady=5)

        tk.Button(action_frame, text="📝 Edit Group", command=self.edit_group,
                 bg='#FF9800', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(action_frame, text="🗑️ Remove Group", command=self.remove_group,
                 bg='#f44336', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(action_frame, text="⭐ Mark Favorite", command=self.mark_favorite,
                 bg='#FFD700', fg='black').pack(side=tk.LEFT, padx=5)

    def setup_analytics(self):
        """Setup Analytics tab"""
        # Campaign history
        history_frame = ttk.LabelFrame(self.analytics_frame, text="📈 Campaign History", padding=10)
        history_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Analytics controls
        control_frame = tk.Frame(history_frame)
        control_frame.pack(fill=tk.X, pady=5)

        tk.Button(control_frame, text="📊 Generate Report", command=self.generate_report,
                 bg='#4CAF50', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(control_frame, text="📤 Export Analytics", command=self.export_analytics,
                 bg='#2196F3', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(control_frame, text="🗑️ Clear History", command=self.clear_history,
                 bg='#f44336', fg='white').pack(side=tk.LEFT, padx=5)

        # Date filter
        tk.Label(control_frame, text="Filter:").pack(side=tk.LEFT, padx=(20,5))
        self.date_filter = ttk.Combobox(control_frame, values=["Last 7 days", "Last 30 days", "All time"],
                                       state="readonly", width=15)
        self.date_filter.set("Last 30 days")
        self.date_filter.pack(side=tk.LEFT, padx=5)
        self.date_filter.bind('<<ComboboxSelected>>', self.filter_analytics)

        # Campaign list
        campaign_columns = ('Date', 'Groups', 'Messages', 'Success Rate', 'Type')
        self.campaign_tree = ttk.Treeview(history_frame, columns=campaign_columns, show='headings', height=8)

        for col in campaign_columns:
            self.campaign_tree.heading(col, text=col)
            self.campaign_tree.column(col, width=100)

        campaign_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.campaign_tree.yview)
        self.campaign_tree.configure(yscrollcommand=campaign_scrollbar.set)

        self.campaign_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, pady=5)
        campaign_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Statistics summary
        stats_frame = ttk.LabelFrame(self.analytics_frame, text="📊 Statistics Summary", padding=10)
        stats_frame.pack(fill=tk.X, padx=10, pady=5)

        self.stats_text = tk.Text(stats_frame, height=6, font=("Courier", 10), state='disabled')
        self.stats_text.pack(fill=tk.X)

    def setup_scheduler(self):
        """Setup Message Scheduler tab"""
        # Schedule creation
        create_frame = ttk.LabelFrame(self.scheduler_frame, text="⏰ Create Scheduled Message", padding=10)
        create_frame.pack(fill=tk.X, padx=10, pady=5)

        # Date and time selection
        datetime_frame = tk.Frame(create_frame)
        datetime_frame.pack(fill=tk.X, pady=5)

        tk.Label(datetime_frame, text="📅 Date:").pack(side=tk.LEFT)
        self.date_entry = tk.Entry(datetime_frame, width=12)
        self.date_entry.pack(side=tk.LEFT, padx=5)
        self.date_entry.insert(0, "YYYY-MM-DD")

        tk.Label(datetime_frame, text="🕐 Time:").pack(side=tk.LEFT, padx=(20,0))
        self.time_entry = tk.Entry(datetime_frame, width=8)
        self.time_entry.pack(side=tk.LEFT, padx=5)
        self.time_entry.insert(0, "HH:MM")

        tk.Button(datetime_frame, text="📅 Pick Date", command=self.pick_date,
                 bg='#2196F3', fg='white').pack(side=tk.LEFT, padx=10)

        # Message content for scheduler
        msg_frame = tk.Frame(create_frame)
        msg_frame.pack(fill=tk.X, pady=5)

        tk.Label(msg_frame, text="📝 Message:").pack(anchor=tk.W)
        self.schedule_message_text = scrolledtext.ScrolledText(msg_frame, height=4)
        self.schedule_message_text.pack(fill=tk.X, pady=5)

        # Group selection for scheduler
        group_sel_frame = tk.Frame(create_frame)
        group_sel_frame.pack(fill=tk.X, pady=5)

        tk.Label(group_sel_frame, text="👥 Target Groups:").pack(side=tk.LEFT)
        self.schedule_groups_var = tk.StringVar()
        schedule_group_options = ["All Groups", "Favorites Only", "By Category", "Custom Selection"]
        self.schedule_groups_combo = ttk.Combobox(group_sel_frame, textvariable=self.schedule_groups_var,
                                                 values=schedule_group_options, state="readonly", width=20)
        self.schedule_groups_combo.set("All Groups")
        self.schedule_groups_combo.pack(side=tk.LEFT, padx=5)

        # Schedule controls
        schedule_btn_frame = tk.Frame(create_frame)
        schedule_btn_frame.pack(fill=tk.X, pady=10)

        tk.Button(schedule_btn_frame, text="⏰ Schedule Message", command=self.schedule_message,
                 bg='#4CAF50', fg='white', font=("Arial", 11)).pack(side=tk.LEFT, padx=5)
        tk.Button(schedule_btn_frame, text="💾 Save as Template", command=self.save_template,
                 bg='#FF9800', fg='white').pack(side=tk.LEFT, padx=5)

        # Scheduled messages list
        scheduled_frame = ttk.LabelFrame(self.scheduler_frame, text="📋 Scheduled Messages", padding=10)
        scheduled_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Scheduled messages controls
        sched_control_frame = tk.Frame(scheduled_frame)
        sched_control_frame.pack(fill=tk.X, pady=5)

        tk.Button(sched_control_frame, text="▶️ Start Scheduler", command=self.start_scheduler,
                 bg='#4CAF50', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(sched_control_frame, text="⏸️ Pause Scheduler", command=self.pause_scheduler,
                 bg='#FF9800', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(sched_control_frame, text="🗑️ Delete Selected", command=self.delete_scheduled,
                 bg='#f44336', fg='white').pack(side=tk.LEFT, padx=5)

        # Scheduler status
        self.scheduler_status = tk.Label(sched_control_frame, text="⏸️ Scheduler: Stopped",
                                        font=("Arial", 10), fg='red')
        self.scheduler_status.pack(side=tk.RIGHT)

        # Scheduled messages tree
        sched_columns = ('Date/Time', 'Message Preview', 'Groups', 'Status')
        self.scheduled_tree = ttk.Treeview(scheduled_frame, columns=sched_columns, show='headings', height=8)

        for col in sched_columns:
            self.scheduled_tree.heading(col, text=col)
            self.scheduled_tree.column(col, width=150)

        sched_scrollbar = ttk.Scrollbar(scheduled_frame, orient=tk.VERTICAL, command=self.scheduled_tree.yview)
        self.scheduled_tree.configure(yscrollcommand=sched_scrollbar.set)

        self.scheduled_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, pady=5)
        sched_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_automation(self):
        """Setup Automation Control tab"""
        # Main control panel
        control_frame = ttk.LabelFrame(self.automation_frame, text="🔄 Campaign Automation Control", padding=15)
        control_frame.pack(fill=tk.X, padx=10, pady=10)

        # Status display
        status_frame = tk.Frame(control_frame)
        status_frame.pack(fill=tk.X, pady=10)

        self.automation_status = tk.Label(status_frame, text="🔴 Automation: STOPPED",
                                         font=("Arial", 14, "bold"), fg='red')
        self.automation_status.pack()

        # Control buttons
        btn_frame = tk.Frame(control_frame)
        btn_frame.pack(pady=20)

        self.start_automation_btn = tk.Button(btn_frame, text="🟢 START AUTOMATION",
                                             command=self.start_automation,
                                             bg='#4CAF50', fg='white',
                                             font=("Arial", 12, "bold"),
                                             width=20, height=2)
        self.start_automation_btn.pack(side=tk.LEFT, padx=10)

        self.stop_automation_btn = tk.Button(btn_frame, text="🔴 STOP AUTOMATION",
                                            command=self.stop_automation,
                                            bg='#f44336', fg='white',
                                            font=("Arial", 12, "bold"),
                                            width=20, height=2)
        self.stop_automation_btn.pack(side=tk.LEFT, padx=10)

        # Automation settings
        settings_frame = ttk.LabelFrame(self.automation_frame, text="⚙️ Automation Settings", padding=10)
        settings_frame.pack(fill=tk.X, padx=10, pady=5)

        # Continuous mode
        self.continuous_mode = tk.BooleanVar(value=False)
        tk.Checkbutton(settings_frame, text="🔁 Run continuously (24/7)",
                      variable=self.continuous_mode,
                      font=("Arial", 11)).pack(anchor=tk.W, pady=5)

        # Auto-commenting
        self.auto_comment = tk.BooleanVar(value=False)
        tk.Checkbutton(settings_frame, text="💬 Auto-comment on pinned messages",
                      variable=self.auto_comment,
                      font=("Arial", 11)).pack(anchor=tk.W, pady=5)

        # Group management
        self.auto_group_mgmt = tk.BooleanVar(value=False)
        tk.Checkbutton(settings_frame, text="👥 Smart group management",
                      variable=self.auto_group_mgmt,
                      font=("Arial", 11)).pack(anchor=tk.W, pady=5)

        # Auto-discovery
        self.auto_discovery = tk.BooleanVar(value=False)
        tk.Checkbutton(settings_frame, text="🔍 Auto-discover new groups",
                      variable=self.auto_discovery,
                      font=("Arial", 11)).pack(anchor=tk.W, pady=5)

        # Timing settings
        timing_frame = tk.Frame(settings_frame)
        timing_frame.pack(fill=tk.X, pady=10)

        tk.Label(timing_frame, text="⏱️ Delay between actions:").pack(side=tk.LEFT)
        self.automation_delay = tk.StringVar(value="30")
        delay_spin = tk.Spinbox(timing_frame, from_=15, to=300, width=10,
                               textvariable=self.automation_delay)
        delay_spin.pack(side=tk.LEFT, padx=10)
        tk.Label(timing_frame, text="seconds").pack(side=tk.LEFT)

        # Campaign queue
        queue_frame = ttk.LabelFrame(self.automation_frame, text="📋 Campaign Queue", padding=10)
        queue_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Queue controls
        queue_btn_frame = tk.Frame(queue_frame)
        queue_btn_frame.pack(fill=tk.X, pady=5)

        tk.Button(queue_btn_frame, text="➕ Add to Queue", command=self.add_to_queue,
                 bg='#2196F3', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(queue_btn_frame, text="🗑️ Clear Queue", command=self.clear_queue,
                 bg='#f44336', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(queue_btn_frame, text="⏸️ Pause Queue", command=self.pause_queue,
                 bg='#FF9800', fg='white').pack(side=tk.LEFT, padx=5)

        # Queue list
        queue_columns = ('Status', 'Message Preview', 'Target Groups', 'Scheduled Time')
        self.queue_tree = ttk.Treeview(queue_frame, columns=queue_columns, show='headings', height=8)

        for col in queue_columns:
            self.queue_tree.heading(col, text=col)
            self.queue_tree.column(col, width=120)

        queue_scrollbar = ttk.Scrollbar(queue_frame, orient=tk.VERTICAL, command=self.queue_tree.yview)
        self.queue_tree.configure(yscrollcommand=queue_scrollbar.set)

        self.queue_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, pady=5)
        queue_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_monitor(self):
        """Setup Live Monitor tab"""
        # Real-time activity monitor
        activity_frame = ttk.LabelFrame(self.monitor_frame, text="📺 Live Activity Monitor", padding=10)
        activity_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Monitor controls
        monitor_btn_frame = tk.Frame(activity_frame)
        monitor_btn_frame.pack(fill=tk.X, pady=5)

        tk.Button(monitor_btn_frame, text="▶️ Start Monitor", command=self.start_monitor,
                 bg='#4CAF50', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(monitor_btn_frame, text="⏸️ Pause Monitor", command=self.pause_monitor,
                 bg='#FF9800', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(monitor_btn_frame, text="🗑️ Clear Log", command=self.clear_monitor,
                 bg='#f44336', fg='white').pack(side=tk.LEFT, padx=5)

        # Auto-scroll toggle
        self.auto_scroll = tk.BooleanVar(value=True)
        tk.Checkbutton(monitor_btn_frame, text="📜 Auto-scroll",
                      variable=self.auto_scroll).pack(side=tk.RIGHT)

        # Live log display
        self.monitor_text = scrolledtext.ScrolledText(activity_frame, height=20,
                                                     font=("Courier", 10),
                                                     bg='black', fg='green')
        self.monitor_text.pack(fill=tk.BOTH, expand=True, pady=5)

        # Statistics panel
        stats_panel = ttk.LabelFrame(self.monitor_frame, text="📊 Live Statistics", padding=10)
        stats_panel.pack(fill=tk.X, padx=10, pady=5)

        stats_grid = tk.Frame(stats_panel)
        stats_grid.pack(fill=tk.X)

        # Stats labels
        self.stats_messages_sent = tk.Label(stats_grid, text="Messages Sent: 0", font=("Arial", 10))
        self.stats_messages_sent.grid(row=0, column=0, padx=20, pady=5)

        self.stats_groups_reached = tk.Label(stats_grid, text="Groups Reached: 0", font=("Arial", 10))
        self.stats_groups_reached.grid(row=0, column=1, padx=20, pady=5)

        self.stats_success_rate = tk.Label(stats_grid, text="Success Rate: 0%", font=("Arial", 10))
        self.stats_success_rate.grid(row=0, column=2, padx=20, pady=5)

        self.stats_uptime = tk.Label(stats_grid, text="Uptime: 00:00:00", font=("Arial", 10))
        self.stats_uptime.grid(row=1, column=0, padx=20, pady=5)

        self.stats_queue_size = tk.Label(stats_grid, text="Queue Size: 0", font=("Arial", 10))
        self.stats_queue_size.grid(row=1, column=1, padx=20, pady=5)

        self.stats_errors = tk.Label(stats_grid, text="Errors: 0", font=("Arial", 10))
        self.stats_errors.grid(row=1, column=2, padx=20, pady=5)

    def setup_discovery(self):
        """Setup Group Discovery tab"""
        # Discovery control panel
        control_frame = ttk.LabelFrame(self.discovery_frame, text="🔍 Group Discovery Control", padding=15)
        control_frame.pack(fill=tk.X, padx=10, pady=10)

        # Discovery status
        status_frame = tk.Frame(control_frame)
        status_frame.pack(fill=tk.X, pady=10)

        self.discovery_status = tk.Label(status_frame, text="🔴 Discovery: STOPPED",
                                        font=("Arial", 14, "bold"), fg='red')
        self.discovery_status.pack()

        # Discovery buttons
        btn_frame = tk.Frame(control_frame)
        btn_frame.pack(pady=20)

        self.start_discovery_btn = tk.Button(btn_frame, text="🔍 START DISCOVERY",
                                            command=self.start_discovery,
                                            bg='#2196F3', fg='white',
                                            font=("Arial", 12, "bold"),
                                            width=18, height=2)
        self.start_discovery_btn.pack(side=tk.LEFT, padx=10)

        self.stop_discovery_btn = tk.Button(btn_frame, text="⏹️ STOP DISCOVERY",
                                           command=self.stop_discovery,
                                           bg='#f44336', fg='white',
                                           font=("Arial", 12, "bold"),
                                           width=18, height=2)
        self.stop_discovery_btn.pack(side=tk.LEFT, padx=10)

        # Discovery settings
        settings_frame = ttk.LabelFrame(self.discovery_frame, text="⚙️ Discovery Settings", padding=10)
        settings_frame.pack(fill=tk.X, padx=10, pady=5)

        # Keywords
        keywords_frame = tk.Frame(settings_frame)
        keywords_frame.pack(fill=tk.X, pady=5)

        tk.Label(keywords_frame, text="🔑 Search Keywords:", font=("Arial", 11, "bold")).pack(anchor=tk.W)
        self.keywords_text = scrolledtext.ScrolledText(keywords_frame, height=3, font=("Arial", 10))
        self.keywords_text.pack(fill=tk.X, pady=5)
        self.keywords_text.insert(1.0, ", ".join(self.discovery_keywords))

        # Discovery interval
        interval_frame = tk.Frame(settings_frame)
        interval_frame.pack(fill=tk.X, pady=10)

        tk.Label(interval_frame, text="⏱️ Discovery interval:").pack(side=tk.LEFT)
        self.discovery_interval = tk.StringVar(value="120")
        interval_spin = tk.Spinbox(interval_frame, from_=60, to=1440, width=10,
                                  textvariable=self.discovery_interval)
        interval_spin.pack(side=tk.LEFT, padx=10)
        tk.Label(interval_frame, text="minutes").pack(side=tk.LEFT)

        # Auto-join settings
        autojoin_frame = tk.Frame(settings_frame)
        autojoin_frame.pack(fill=tk.X, pady=5)

        self.auto_join_discovered = tk.BooleanVar(value=False)
        tk.Checkbutton(autojoin_frame, text="🤝 Auto-join discovered groups",
                      variable=self.auto_join_discovered,
                      font=("Arial", 11)).pack(anchor=tk.W)

        self.auto_evaluate_groups = tk.BooleanVar(value=True)
        tk.Checkbutton(autojoin_frame, text="📊 Evaluate group quality before joining",
                      variable=self.auto_evaluate_groups,
                      font=("Arial", 11)).pack(anchor=tk.W)

        self.auto_confirm_join = tk.BooleanVar(value=True)
        tk.Checkbutton(autojoin_frame, text="✅ Auto-confirm join requests",
                      variable=self.auto_confirm_join,
                      font=("Arial", 11)).pack(anchor=tk.W)

        # Join method settings
        join_method_frame = tk.Frame(settings_frame)
        join_method_frame.pack(fill=tk.X, pady=10)

        tk.Label(join_method_frame, text="🔗 Join Method:", font=("Arial", 11, "bold")).pack(anchor=tk.W)

        self.join_method = tk.StringVar(value="auto")
        join_methods = [
            ("🤖 Auto-detect", "auto"),
            ("👤 Username", "username"),
            ("🔗 Invite Link", "invite"),
            ("🔍 Search", "search")
        ]

        for text, value in join_methods:
            tk.Radiobutton(join_method_frame, text=text, variable=self.join_method,
                          value=value, font=("Arial", 10)).pack(anchor=tk.W, padx=20)

        # Discovered groups list
        discovered_frame = ttk.LabelFrame(self.discovery_frame, text="📋 Discovered Groups", padding=10)
        discovered_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Discovery controls
        disc_btn_frame = tk.Frame(discovered_frame)
        disc_btn_frame.pack(fill=tk.X, pady=5)

        tk.Button(disc_btn_frame, text="🔄 Manual Search", command=self.manual_search,
                 bg='#4CAF50', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(disc_btn_frame, text="✅ Join Selected", command=self.join_selected,
                 bg='#2196F3', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(disc_btn_frame, text="❌ Ignore Selected", command=self.ignore_selected,
                 bg='#FF9800', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(disc_btn_frame, text="🗑️ Clear List", command=self.clear_discovered,
                 bg='#f44336', fg='white').pack(side=tk.LEFT, padx=5)

        # Discovered groups tree
        disc_columns = ('Name', 'Members', 'Type', 'Keywords', 'Status', 'Quality')
        self.discovered_tree = ttk.Treeview(discovered_frame, columns=disc_columns, show='headings', height=10)

        for col in disc_columns:
            self.discovered_tree.heading(col, text=col)
            self.discovered_tree.column(col, width=100)

        disc_scrollbar = ttk.Scrollbar(discovered_frame, orient=tk.VERTICAL, command=self.discovered_tree.yview)
        self.discovered_tree.configure(yscrollcommand=disc_scrollbar.set)

        self.discovered_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, pady=5)
        disc_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Discovery statistics
        disc_stats_frame = ttk.LabelFrame(self.discovery_frame, text="📊 Discovery Statistics", padding=10)
        disc_stats_frame.pack(fill=tk.X, padx=10, pady=5)

        disc_stats_grid = tk.Frame(disc_stats_frame)
        disc_stats_grid.pack(fill=tk.X)

        self.disc_stats_found = tk.Label(disc_stats_grid, text="Groups Found: 0", font=("Arial", 10))
        self.disc_stats_found.grid(row=0, column=0, padx=20, pady=5)

        self.disc_stats_joined = tk.Label(disc_stats_grid, text="Groups Joined: 0", font=("Arial", 10))
        self.disc_stats_joined.grid(row=0, column=1, padx=20, pady=5)

        self.disc_stats_quality = tk.Label(disc_stats_grid, text="Avg Quality: 0%", font=("Arial", 10))
        self.disc_stats_quality.grid(row=0, column=2, padx=20, pady=5)

    # Group Manager Methods
    def add_category(self):
        """Add new group category"""
        from tkinter import simpledialog
        category = simpledialog.askstring("Add Category", "Enter category name:")
        if category and category not in self.group_categories:
            self.group_categories[category] = []
            self.update_category_list()
            self.log_message(f"Added category: {category}")

    def edit_category(self):
        """Edit selected category"""
        selection = self.category_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a category to edit")
            return

        old_name = self.category_listbox.get(selection[0])
        from tkinter import simpledialog
        new_name = simpledialog.askstring("Edit Category", "Enter new name:", initialvalue=old_name)

        if new_name and new_name != old_name:
            self.group_categories[new_name] = self.group_categories.pop(old_name)
            self.update_category_list()
            self.log_message(f"Renamed category: {old_name} → {new_name}")

    def delete_category(self):
        """Delete selected category"""
        selection = self.category_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a category to delete")
            return

        category = self.category_listbox.get(selection[0])
        if messagebox.askyesno("Confirm Delete", f"Delete category '{category}' and all its groups?"):
            del self.group_categories[category]
            self.update_category_list()
            self.update_group_tree()
            self.log_message(f"Deleted category: {category}")

    def update_category_list(self):
        """Update category listbox"""
        self.category_listbox.delete(0, tk.END)
        for category in sorted(self.group_categories.keys()):
            self.category_listbox.insert(tk.END, category)

    def on_category_select(self, event):
        """Handle category selection"""
        self.update_group_tree()

    def import_groups(self):
        """Import groups from CSV file"""
        from tkinter import filedialog
        filename = filedialog.askopenfilename(
            title="Import Groups",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if filename:
            try:
                import csv
                with open(filename, 'r', encoding='utf-8') as file:
                    reader = csv.DictReader(file)
                    imported = 0
                    for row in reader:
                        name = row.get('name', '')
                        category = row.get('category', 'Imported')
                        group_type = row.get('type', 'Group')

                        if name:
                            if category not in self.group_categories:
                                self.group_categories[category] = []

                            group_info = {
                                'name': name,
                                'type': group_type,
                                'status': 'Active',
                                'last_used': 'Never'
                            }
                            self.group_categories[category].append(group_info)
                            imported += 1

                self.update_category_list()
                self.update_group_tree()
                self.log_message(f"Imported {imported} groups from CSV")
                messagebox.showinfo("Import Complete", f"Successfully imported {imported} groups")

            except Exception as e:
                self.log_message(f"Import error: {str(e)}")
                messagebox.showerror("Import Error", f"Failed to import groups: {str(e)}")

    def export_groups(self):
        """Export groups to CSV file"""
        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            title="Export Groups",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if filename:
            try:
                import csv
                with open(filename, 'w', newline='', encoding='utf-8') as file:
                    writer = csv.writer(file)
                    writer.writerow(['name', 'type', 'category', 'status', 'last_used'])

                    exported = 0
                    for category, groups in self.group_categories.items():
                        for group in groups:
                            writer.writerow([
                                group['name'],
                                group['type'],
                                category,
                                group['status'],
                                group['last_used']
                            ])
                            exported += 1

                self.log_message(f"Exported {exported} groups to CSV")
                messagebox.showinfo("Export Complete", f"Successfully exported {exported} groups")

            except Exception as e:
                self.log_message(f"Export error: {str(e)}")
                messagebox.showerror("Export Error", f"Failed to export groups: {str(e)}")

    def sync_groups(self):
        """Sync groups with current Telegram groups"""
        if not self.is_connected:
            messagebox.showerror("Error", "Please connect to Telegram first")
            return

        # Add current groups to "Synced" category
        if "Synced" not in self.group_categories:
            self.group_categories["Synced"] = []

        synced = 0
        for group in self.groups:
            # Check if group already exists
            exists = False
            for category_groups in self.group_categories.values():
                if any(g['name'] == group['name'] for g in category_groups):
                    exists = True
                    break

            if not exists:
                group_info = {
                    'name': group['name'],
                    'type': group['type'],
                    'status': 'Active',
                    'last_used': 'Never'
                }
                self.group_categories["Synced"].append(group_info)
                synced += 1

        self.update_category_list()
        self.update_group_tree()
        self.log_message(f"Synced {synced} new groups from Telegram")
        messagebox.showinfo("Sync Complete", f"Added {synced} new groups to 'Synced' category")

    def update_group_tree(self):
        """Update group tree view"""
        # Clear existing items
        for item in self.group_tree.get_children():
            self.group_tree.delete(item)

        # Get selected category
        selection = self.category_listbox.curselection()
        if selection:
            category = self.category_listbox.get(selection[0])
            groups = self.group_categories.get(category, [])
        else:
            # Show all groups
            groups = []
            for category_groups in self.group_categories.values():
                groups.extend([(g, cat) for g, cat in zip(category_groups, [category] * len(category_groups))])

        # Add groups to tree
        if selection:
            for group in groups:
                self.group_tree.insert('', tk.END, values=(
                    group['name'],
                    group['type'],
                    category,
                    group['status'],
                    group['last_used']
                ))
        else:
            for group, cat in groups:
                self.group_tree.insert('', tk.END, values=(
                    group['name'],
                    group['type'],
                    cat,
                    group['status'],
                    group['last_used']
                ))

    def edit_group(self):
        """Edit selected group"""
        selection = self.group_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a group to edit")
            return

        # Implementation for editing group details
        messagebox.showinfo("Edit Group", "Group editing feature - coming soon!")

    def remove_group(self):
        """Remove selected group"""
        selection = self.group_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a group to remove")
            return

        item = self.group_tree.item(selection[0])
        group_name = item['values'][0]
        category = item['values'][2]

        if messagebox.askyesno("Confirm Remove", f"Remove '{group_name}' from category '{category}'?"):
            # Remove from category
            if category in self.group_categories:
                self.group_categories[category] = [
                    g for g in self.group_categories[category]
                    if g['name'] != group_name
                ]
            self.update_group_tree()
            self.log_message(f"Removed group: {group_name}")

    def mark_favorite(self):
        """Mark selected group as favorite"""
        selection = self.group_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a group to mark as favorite")
            return

        # Add to Favorites category
        if "⭐ Favorites" not in self.group_categories:
            self.group_categories["⭐ Favorites"] = []

        item = self.group_tree.item(selection[0])
        group_name = item['values'][0]
        group_type = item['values'][1]

        # Check if already in favorites
        if any(g['name'] == group_name for g in self.group_categories["⭐ Favorites"]):
            messagebox.showinfo("Info", "Group is already in favorites")
            return

        favorite_group = {
            'name': group_name,
            'type': group_type,
            'status': 'Favorite',
            'last_used': 'Never'
        }
        self.group_categories["⭐ Favorites"].append(favorite_group)
        self.update_category_list()
        self.log_message(f"Added to favorites: {group_name}")

    # Analytics Methods
    def generate_report(self):
        """Generate analytics report"""
        if not self.campaign_history:
            messagebox.showinfo("No Data", "No campaign history available")
            return

        # Calculate statistics
        total_campaigns = len(self.campaign_history)
        total_messages = sum(c.get('messages_sent', 0) for c in self.campaign_history)
        total_groups = sum(c.get('groups_targeted', 0) for c in self.campaign_history)
        avg_success_rate = sum(c.get('success_rate', 0) for c in self.campaign_history) / total_campaigns

        # Update stats display
        self.stats_text.config(state='normal')
        self.stats_text.delete(1.0, tk.END)

        report = f"""📊 CAMPAIGN ANALYTICS REPORT
{'='*50}
📈 Total Campaigns: {total_campaigns}
📤 Total Messages Sent: {total_messages:,}
👥 Total Groups Reached: {total_groups:,}
📊 Average Success Rate: {avg_success_rate:.1f}%

🏆 BEST PERFORMING CAMPAIGNS:
"""

        # Sort by success rate
        best_campaigns = sorted(self.campaign_history,
                               key=lambda x: x.get('success_rate', 0),
                               reverse=True)[:5]

        for i, campaign in enumerate(best_campaigns, 1):
            report += f"{i}. {campaign.get('date', 'Unknown')} - {campaign.get('success_rate', 0):.1f}% success\n"

        self.stats_text.insert(1.0, report)
        self.stats_text.config(state='disabled')

        self.log_message("Generated analytics report")

    def export_analytics(self):
        """Export analytics to CSV"""
        if not self.campaign_history:
            messagebox.showinfo("No Data", "No campaign history to export")
            return

        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            title="Export Analytics",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if filename:
            try:
                import csv
                with open(filename, 'w', newline='', encoding='utf-8') as file:
                    writer = csv.writer(file)
                    writer.writerow(['Date', 'Groups', 'Messages', 'Success Rate', 'Type', 'Duration'])

                    for campaign in self.campaign_history:
                        writer.writerow([
                            campaign.get('date', ''),
                            campaign.get('groups_targeted', 0),
                            campaign.get('messages_sent', 0),
                            campaign.get('success_rate', 0),
                            campaign.get('message_type', 'Text'),
                            campaign.get('duration', 0)
                        ])

                self.log_message("Exported analytics to CSV")
                messagebox.showinfo("Export Complete", "Analytics exported successfully")

            except Exception as e:
                self.log_message(f"Export error: {str(e)}")
                messagebox.showerror("Export Error", f"Failed to export analytics: {str(e)}")

    def clear_history(self):
        """Clear campaign history"""
        if messagebox.askyesno("Confirm Clear", "Clear all campaign history? This cannot be undone."):
            self.campaign_history.clear()
            self.update_campaign_tree()
            self.stats_text.config(state='normal')
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, "Campaign history cleared.")
            self.stats_text.config(state='disabled')
            self.log_message("Cleared campaign history")

    def filter_analytics(self, event=None):
        """Filter analytics by date range"""
        self.update_campaign_tree()

    def update_campaign_tree(self):
        """Update campaign tree view"""
        # Clear existing items
        for item in self.campaign_tree.get_children():
            self.campaign_tree.delete(item)

        # Filter by date range
        filter_value = self.date_filter.get()
        filtered_campaigns = self.campaign_history  # For now, show all

        # Add campaigns to tree
        for campaign in filtered_campaigns:
            self.campaign_tree.insert('', tk.END, values=(
                campaign.get('date', ''),
                campaign.get('groups_targeted', 0),
                campaign.get('messages_sent', 0),
                f"{campaign.get('success_rate', 0):.1f}%",
                campaign.get('message_type', 'Text')
            ))

    # Scheduler Methods
    def pick_date(self):
        """Open date picker"""
        try:
            import tkinter.ttk as ttk
            from datetime import datetime, timedelta

            # Simple date picker - set to tomorrow
            tomorrow = datetime.now() + timedelta(days=1)
            self.date_entry.delete(0, tk.END)
            self.date_entry.insert(0, tomorrow.strftime("%Y-%m-%d"))

            # Set default time to current time + 1 hour
            default_time = (datetime.now() + timedelta(hours=1)).strftime("%H:%M")
            self.time_entry.delete(0, tk.END)
            self.time_entry.insert(0, default_time)

        except Exception as e:
            self.log_message(f"Date picker error: {str(e)}")

    def schedule_message(self):
        """Schedule a message for later sending"""
        date_str = self.date_entry.get().strip()
        time_str = self.time_entry.get().strip()
        message = self.schedule_message_text.get(1.0, tk.END).strip()
        target_groups = self.schedule_groups_var.get()

        if not date_str or date_str == "YYYY-MM-DD":
            messagebox.showerror("Error", "Please enter a valid date")
            return

        if not time_str or time_str == "HH:MM":
            messagebox.showerror("Error", "Please enter a valid time")
            return

        if not message:
            messagebox.showerror("Error", "Please enter a message")
            return

        try:
            from datetime import datetime
            # Parse datetime
            datetime_str = f"{date_str} {time_str}"
            scheduled_time = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M")

            # Check if time is in the future
            if scheduled_time <= datetime.now():
                messagebox.showerror("Error", "Scheduled time must be in the future")
                return

            # Create scheduled message
            scheduled_msg = {
                'id': len(self.scheduled_messages) + 1,
                'datetime': scheduled_time,
                'message': message,
                'target_groups': target_groups,
                'status': 'Pending',
                'created': datetime.now()
            }

            self.scheduled_messages.append(scheduled_msg)
            self.update_scheduled_tree()

            # Clear form
            self.schedule_message_text.delete(1.0, tk.END)

            self.log_message(f"Scheduled message for {datetime_str}")
            messagebox.showinfo("Success", f"Message scheduled for {datetime_str}")

        except ValueError as e:
            messagebox.showerror("Error", "Invalid date/time format. Use YYYY-MM-DD and HH:MM")
        except Exception as e:
            self.log_message(f"Scheduling error: {str(e)}")
            messagebox.showerror("Error", f"Failed to schedule message: {str(e)}")

    def save_template(self):
        """Save message as template"""
        message = self.schedule_message_text.get(1.0, tk.END).strip()
        if not message:
            messagebox.showerror("Error", "Please enter a message to save as template")
            return

        from tkinter import simpledialog
        template_name = simpledialog.askstring("Save Template", "Enter template name:")

        if template_name:
            # Save to templates (could be expanded to file storage)
            self.log_message(f"Saved template: {template_name}")
            messagebox.showinfo("Success", f"Template '{template_name}' saved")

    def start_scheduler(self):
        """Start the message scheduler"""
        self.scheduler_status.config(text="▶️ Scheduler: Running", fg='green')
        self.log_message("Message scheduler started")
        # In a real implementation, this would start a background thread
        # to check for scheduled messages and send them

    def pause_scheduler(self):
        """Pause the message scheduler"""
        self.scheduler_status.config(text="⏸️ Scheduler: Paused", fg='orange')
        self.log_message("Message scheduler paused")

    def delete_scheduled(self):
        """Delete selected scheduled message"""
        selection = self.scheduled_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a scheduled message to delete")
            return

        item = self.scheduled_tree.item(selection[0])
        msg_id = int(item['values'][0]) if item['values'][0].isdigit() else None

        if msg_id and messagebox.askyesno("Confirm Delete", "Delete selected scheduled message?"):
            self.scheduled_messages = [msg for msg in self.scheduled_messages if msg.get('id') != msg_id]
            self.update_scheduled_tree()
            self.log_message(f"Deleted scheduled message ID: {msg_id}")

    def update_scheduled_tree(self):
        """Update scheduled messages tree view"""
        # Clear existing items
        for item in self.scheduled_tree.get_children():
            self.scheduled_tree.delete(item)

        # Add scheduled messages to tree
        for msg in sorted(self.scheduled_messages, key=lambda x: x['datetime']):
            datetime_str = msg['datetime'].strftime("%Y-%m-%d %H:%M")
            preview = msg['message'][:50] + "..." if len(msg['message']) > 50 else msg['message']

            self.scheduled_tree.insert('', tk.END, values=(
                datetime_str,
                preview,
                msg['target_groups'],
                msg['status']
            ))

    # Automation Control Methods
    def start_automation(self):
        """Start the automation system"""
        if self.automation_running:
            messagebox.showinfo("Info", "Automation is already running")
            return

        if not self.is_connected:
            messagebox.showerror("Error", "Please connect to Telegram first")
            return

        self.automation_running = True
        self.automation_status.config(text="🟢 Automation: RUNNING", fg='green')
        self.start_automation_btn.config(state='disabled')
        self.stop_automation_btn.config(state='normal')

        # Start automation thread
        import threading
        self.automation_thread = threading.Thread(target=self.automation_worker, daemon=True)
        self.automation_thread.start()

        self.log_message("🟢 Automation started")
        self.monitor_log("🟢 AUTOMATION STARTED - Continuous mode enabled")

    def stop_automation(self):
        """Stop the automation system"""
        self.automation_running = False
        self.automation_status.config(text="🔴 Automation: STOPPED", fg='red')
        self.start_automation_btn.config(state='normal')
        self.stop_automation_btn.config(state='disabled')

        self.log_message("🔴 Automation stopped")
        self.monitor_log("🔴 AUTOMATION STOPPED - All activities paused")

    def automation_worker(self):
        """Main automation worker thread"""
        import time
        import random

        self.monitor_log("🔄 Automation worker started")

        while self.automation_running:
            try:
                # Process scheduled messages
                self.process_scheduled_messages()

                # Smart group management
                if self.auto_group_mgmt.get():
                    self.smart_group_management()

                # Auto-commenting on pinned messages
                if self.auto_comment.get():
                    self.auto_comment_pinned()

                # Auto-discovery of new groups
                if self.auto_discovery.get():
                    self.auto_discover_groups()

                # Process campaign queue
                self.process_campaign_queue()

                # Smart delay between operations
                delay = int(self.automation_delay.get())
                random_delay = random.randint(delay, delay + 15)  # Add randomness

                self.monitor_log(f"⏱️ Waiting {random_delay} seconds before next cycle...")

                for i in range(random_delay):
                    if not self.automation_running:
                        break
                    time.sleep(1)

            except Exception as e:
                self.log_message(f"❌ Automation error: {str(e)}")
                self.monitor_log(f"❌ ERROR: {str(e)}")

                if not self.continuous_mode.get():
                    break

        self.monitor_log("🔄 Automation worker stopped")

    def process_scheduled_messages(self):
        """Process scheduled messages that are due"""
        from datetime import datetime

        current_time = datetime.now()
        due_messages = []

        for msg in self.scheduled_messages:
            if msg['datetime'] <= current_time and msg['status'] == 'Pending':
                due_messages.append(msg)

        for msg in due_messages:
            try:
                self.monitor_log(f"📤 Processing scheduled message: {msg['message'][:50]}...")

                # Update message status
                msg['status'] = 'Sending'
                self.root.after(0, self.update_scheduled_tree)

                # Send the actual message
                try:
                    future = asyncio.run_coroutine_threadsafe(
                        self.send_scheduled_message_real(msg),
                        self.event_loop
                    )
                    success = future.result(timeout=60)
                    if success:
                        self.monitor_log(f"✅ Scheduled message sent successfully")
                    else:
                        self.monitor_log(f"❌ Failed to send scheduled message")
                except Exception as e:
                    self.monitor_log(f"❌ Scheduled send error: {str(e)}")
                    msg['status'] = 'Failed'

                msg['status'] = 'Sent'
                self.root.after(0, self.update_scheduled_tree)

            except Exception as e:
                self.monitor_log(f"❌ Failed to send scheduled message: {str(e)}")
                msg['status'] = 'Failed'
                self.root.after(0, self.update_scheduled_tree)

    async def send_scheduled_message_real(self, scheduled_msg):
        """Actually send a scheduled message to real groups"""
        try:
            message_text = scheduled_msg['message']
            target_groups = scheduled_msg['target_groups']

            # Determine which groups to send to
            if target_groups == "All Groups":
                target_entities = [group['entity'] for group in self.groups]
            elif target_groups == "Favorites Only":
                favorite_groups = self.group_categories.get("⭐ Favorites", [])
                target_entities = []
                for fav_group in favorite_groups:
                    # Find matching entity
                    for group in self.groups:
                        if group['name'] == fav_group['name']:
                            target_entities.append(group['entity'])
                            break
            else:
                # Use current selected groups
                target_entities = [group['entity'] for group in self.groups[:5]]  # Limit for safety

            if not target_entities:
                self.monitor_log("⚠️ No target groups found for scheduled message")
                return False

            # Send to each group
            sent_count = 0
            for entity in target_entities:
                try:
                    await self.client.send_message(entity, message_text)
                    sent_count += 1
                    self.monitor_log(f"📤 Sent scheduled message to group {sent_count}")

                    # Delay between sends
                    await asyncio.sleep(2)

                except Exception as e:
                    self.monitor_log(f"❌ Failed to send to group: {str(e)}")
                    continue

            self.monitor_log(f"✅ Scheduled message sent to {sent_count} groups")
            return sent_count > 0

        except Exception as e:
            self.monitor_log(f"❌ Scheduled send error: {str(e)}")
            return False

    def smart_group_management(self):
        """Intelligent group management"""
        self.monitor_log("🧠 Running smart group management...")

        # Analyze group performance
        active_groups = 0
        for category, groups in self.group_categories.items():
            for group in groups:
                if group['status'] == 'Active':
                    active_groups += 1

        self.monitor_log(f"📊 Found {active_groups} active groups across {len(self.group_categories)} categories")

        # Update group statistics
        self.root.after(0, lambda: self.stats_groups_reached.config(text=f"Groups Managed: {active_groups}"))

    def auto_comment_pinned(self):
        """Auto-comment on real pinned messages"""
        self.monitor_log("💬 Checking for real pinned messages...")

        try:
            # Run the real pinned message check
            future = asyncio.run_coroutine_threadsafe(
                self.check_and_comment_pinned(),
                self.event_loop
            )
            future.result(timeout=60)

        except Exception as e:
            self.monitor_log(f"❌ Auto-comment error: {str(e)}")

    async def check_and_comment_pinned(self):
        """Check for real pinned messages and comment"""
        commented = 0

        try:
            # Get your groups
            async for dialog in self.client.iter_dialogs():
                if not (hasattr(dialog.entity, 'megagroup') or hasattr(dialog.entity, 'broadcast')):
                    continue

                try:
                    # Check if group has pinned message
                    if dialog.pinned_msg_id:
                        self.monitor_log(f"📌 Found pinned message in: {dialog.name}")

                        # Get the pinned message
                        pinned_msg = await self.client.get_messages(
                            dialog.entity,
                            ids=dialog.pinned_msg_id
                        )

                        if pinned_msg and pinned_msg.text:
                            # Check if it's a relevant message (contains keywords)
                            msg_text = pinned_msg.text.lower()
                            relevant_keywords = ['call', 'gem', 'pump', 'entry', 'target', 'signal', 'alert']

                            if any(keyword in msg_text for keyword in relevant_keywords):
                                self.monitor_log(f"🎯 Relevant pinned message found: {pinned_msg.text[:50]}...")

                                # Post a comment
                                success = await self.post_comment_on_message(dialog.entity, pinned_msg)
                                if success:
                                    commented += 1

                                # Delay between comments
                                await asyncio.sleep(30)

                except Exception as e:
                    self.monitor_log(f"❌ Error checking {dialog.name}: {str(e)}")
                    continue

        except Exception as e:
            self.monitor_log(f"❌ Pinned message check error: {str(e)}")

        self.monitor_log(f"💬 Commented on {commented} pinned messages")

    async def post_comment_on_message(self, entity, message):
        """Post a real comment on a message"""
        try:
            comment_templates = [
                "Great analysis! 📈",
                "Thanks for sharing! 🙏",
                "Interesting perspective 🤔",
                "Looking forward to updates! 👀",
                "Solid information 💪",
                "Nice call! 🚀",
                "Appreciate the insight 💎",
                "Good timing on this 📊"
            ]

            import random
            comment = random.choice(comment_templates)

            # Check if the message has a discussion group (for channels)
            if hasattr(message, 'replies') and message.replies and message.replies.comments:
                # Post in discussion group
                discussion_entity = await self.client.get_entity(message.replies.channel_id)
                await self.client.send_message(
                    discussion_entity,
                    comment,
                    reply_to=message.replies.max_id
                )
                self.monitor_log(f"💬 Posted comment in discussion: '{comment}'")
            else:
                # Post as regular reply
                await self.client.send_message(
                    entity,
                    comment,
                    reply_to=message.id
                )
                self.monitor_log(f"💬 Posted reply: '{comment}'")

            return True

        except Exception as e:
            self.monitor_log(f"❌ Comment failed: {str(e)}")
            return False

    def process_campaign_queue(self):
        """Process the campaign queue"""
        # Check if there are campaigns in queue
        queue_items = len(self.queue_tree.get_children())

        if queue_items > 0:
            self.monitor_log(f"📋 Processing campaign queue ({queue_items} items)...")
            self.root.after(0, lambda: self.stats_queue_size.config(text=f"Queue Size: {queue_items}"))

        # Process queue items (simplified)
        # In real implementation, this would process actual campaigns

    def add_to_queue(self):
        """Add current message to automation queue"""
        message = self.message_text.get(1.0, tk.END).strip()
        if not message:
            messagebox.showerror("Error", "Please enter a message first")
            return

        # Add to queue
        from datetime import datetime
        queue_item = {
            'status': 'Queued',
            'message': message[:50] + "..." if len(message) > 50 else message,
            'groups': 'Selected Groups',
            'time': datetime.now().strftime("%H:%M:%S")
        }

        self.queue_tree.insert('', tk.END, values=(
            queue_item['status'],
            queue_item['message'],
            queue_item['groups'],
            queue_item['time']
        ))

        self.log_message("➕ Message added to automation queue")
        self.monitor_log(f"➕ Added to queue: {queue_item['message']}")

    def clear_queue(self):
        """Clear the campaign queue"""
        for item in self.queue_tree.get_children():
            self.queue_tree.delete(item)

        self.log_message("🗑️ Campaign queue cleared")
        self.monitor_log("🗑️ Campaign queue cleared")

    def pause_queue(self):
        """Pause queue processing"""
        self.log_message("⏸️ Queue processing paused")
        self.monitor_log("⏸️ Queue processing paused")

    # Monitor Methods
    def start_monitor(self):
        """Start the live monitor"""
        self.monitor_log("📺 Live monitor started")
        self.log_message("📺 Live monitor activated")

    def pause_monitor(self):
        """Pause the live monitor"""
        self.monitor_log("⏸️ Live monitor paused")
        self.log_message("⏸️ Live monitor paused")

    def clear_monitor(self):
        """Clear the monitor log"""
        self.monitor_text.delete(1.0, tk.END)
        self.log_message("🗑️ Monitor log cleared")

    def monitor_log(self, message):
        """Add message to monitor log"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.monitor_text.insert(tk.END, log_entry)

        if self.auto_scroll.get():
            self.monitor_text.see(tk.END)

        # Update stats
        self.update_monitor_stats()

    def update_monitor_stats(self):
        """Update live statistics"""
        # This would update real-time statistics
        # For now, just update uptime
        pass

    # Group Discovery Methods
    def start_discovery(self):
        """Start group discovery"""
        if self.discovery_running:
            messagebox.showinfo("Info", "Discovery is already running")
            return

        if not self.is_connected:
            messagebox.showerror("Error", "Please connect to Telegram first")
            return

        self.discovery_running = True
        self.discovery_status.config(text="🔍 Discovery: RUNNING", fg='blue')
        self.start_discovery_btn.config(state='disabled')
        self.stop_discovery_btn.config(state='normal')

        # Start discovery thread
        import threading
        self.discovery_thread = threading.Thread(target=self.discovery_worker, daemon=True)
        self.discovery_thread.start()

        self.log_message("🔍 Group discovery started")
        self.monitor_log("🔍 GROUP DISCOVERY STARTED - Searching for new groups")

    def stop_discovery(self):
        """Stop group discovery"""
        self.discovery_running = False
        self.discovery_status.config(text="🔴 Discovery: STOPPED", fg='red')
        self.start_discovery_btn.config(state='normal')
        self.stop_discovery_btn.config(state='disabled')

        self.log_message("🔴 Group discovery stopped")
        self.monitor_log("🔴 GROUP DISCOVERY STOPPED")

    def discovery_worker(self):
        """Main discovery worker thread"""
        import time

        self.monitor_log("🔍 Discovery worker started")

        while self.discovery_running:
            try:
                # Run discovery cycle
                self.run_discovery_cycle()

                # Wait for next cycle
                interval_minutes = int(self.discovery_interval.get())
                self.monitor_log(f"⏱️ Next discovery cycle in {interval_minutes} minutes...")

                # Sleep in small intervals to allow stopping
                for i in range(interval_minutes * 60):
                    if not self.discovery_running:
                        break
                    time.sleep(1)

            except Exception as e:
                self.log_message(f"❌ Discovery error: {str(e)}")
                self.monitor_log(f"❌ DISCOVERY ERROR: {str(e)}")

                # Wait before retrying
                time.sleep(60)

        self.monitor_log("🔍 Discovery worker stopped")

    def run_discovery_cycle(self):
        """Run a single discovery cycle"""
        self.monitor_log("🔍 Starting discovery cycle...")

        # Get keywords
        keywords_text = self.keywords_text.get(1.0, tk.END).strip()
        keywords = [k.strip() for k in keywords_text.split(',') if k.strip()]

        if not keywords:
            self.monitor_log("⚠️ No keywords specified for discovery")
            return

        # Search for groups using different methods
        found_groups = []

        # Method 1: Search through your existing contacts for similar groups
        found_groups.extend(self.search_similar_groups(keywords))

        # Method 2: Analyze group names and descriptions
        found_groups.extend(self.analyze_existing_groups(keywords))

        # Method 3: Search public group directories (simulated)
        found_groups.extend(self.search_public_directories(keywords))

        # Process discovered groups
        for group in found_groups:
            self.process_discovered_group(group)

        self.monitor_log(f"🔍 Discovery cycle complete - Found {len(found_groups)} potential groups")

        # Update statistics
        self.root.after(0, self.update_discovery_stats)

    def search_similar_groups(self, keywords):
        """Search for similar groups in your contacts"""
        self.monitor_log("🔍 Searching similar groups in contacts...")

        similar_groups = []

        # Analyze existing groups for keyword matches
        for group in self.groups:
            group_name = group['name'].lower()
            group_desc = getattr(group, 'description', '').lower()

            # Check if group matches keywords
            matches = 0
            for keyword in keywords:
                if keyword.lower() in group_name or keyword.lower() in group_desc:
                    matches += 1

            if matches > 0:
                quality_score = min(100, (matches / len(keywords)) * 100)
                similar_groups.append({
                    'name': f"Similar to {group['name']}",
                    'members': 'Unknown',
                    'type': 'Similar',
                    'keywords': f"{matches} matches",
                    'quality': quality_score,
                    'source': 'contacts'
                })

        return similar_groups[:5]  # Limit results

    def analyze_existing_groups(self, keywords):
        """Analyze existing groups for patterns"""
        self.monitor_log("🔍 Analyzing existing group patterns...")

        pattern_groups = []

        # Look for patterns in group names
        common_patterns = ['pump', 'moon', 'gem', 'calls', 'signals', 'alpha']

        for pattern in common_patterns:
            if any(keyword.lower() in pattern for keyword in keywords):
                pattern_groups.append({
                    'name': f"Groups with '{pattern}' pattern",
                    'members': 'Various',
                    'type': 'Pattern',
                    'keywords': pattern,
                    'quality': 75,
                    'source': 'pattern'
                })

        return pattern_groups[:3]  # Limit results

    def search_public_directories(self, keywords):
        """Search for real groups using Telegram's search functionality"""
        self.monitor_log("🔍 Searching Telegram for real groups...")

        found_groups = []

        try:
            # Use the actual Telegram client to search for groups
            future = asyncio.run_coroutine_threadsafe(
                self.search_telegram_groups(keywords),
                self.event_loop
            )
            found_groups = future.result(timeout=30)

        except Exception as e:
            self.monitor_log(f"❌ Search error: {str(e)}")

        return found_groups

    async def search_telegram_groups(self, keywords):
        """Actually search Telegram for groups with keywords"""
        found_groups = []

        try:
            # Method 1: Search through global search
            for keyword in keywords[:3]:  # Limit to avoid rate limits
                self.monitor_log(f"🔍 Searching Telegram globally for: {keyword}")

                try:
                    # Use iter_dialogs to find groups with keywords in name
                    async for dialog in self.client.iter_dialogs():
                        if not dialog.is_group and not dialog.is_channel:
                            continue

                        # Check if keyword matches group name or description
                        group_name = dialog.name.lower()
                        if keyword.lower() in group_name:
                            entity = dialog.entity

                            # Get member count
                            try:
                                full_info = await self.client.get_entity(entity)
                                member_count = getattr(full_info, 'participants_count', 0)
                            except:
                                member_count = 0

                            # Calculate quality
                            quality = min(100, max(40, (member_count / 500) * 30 + 40))

                            # Check if we're already a member
                            try:
                                participants = await self.client.get_participants(entity, limit=1)
                                is_member = True
                            except:
                                is_member = False

                            if not is_member:  # Only add groups we're not in
                                found_groups.append({
                                    'name': dialog.name,
                                    'members': str(member_count) if member_count else 'Unknown',
                                    'type': 'Supergroup' if getattr(entity, 'megagroup', False) else 'Channel',
                                    'keywords': keyword,
                                    'quality': quality,
                                    'source': 'dialog_search',
                                    'entity': entity,
                                    'username': getattr(entity, 'username', None),
                                    'invite_link': None
                                })

                                self.monitor_log(f"📋 Found potential group: {dialog.name} ({member_count} members)")

                    # Small delay between keyword searches
                    await asyncio.sleep(3)

                except Exception as e:
                    self.monitor_log(f"❌ Search failed for '{keyword}': {str(e)}")
                    continue

            # Method 2: Search for public groups with usernames
            await self.search_public_usernames(keywords, found_groups)

            # Method 3: Search for invite links in your existing groups
            await self.search_invite_links_in_groups(keywords, found_groups)

        except Exception as e:
            self.monitor_log(f"❌ Telegram search error: {str(e)}")

        self.monitor_log(f"🔍 Found {len(found_groups)} potential groups to join")
        return found_groups[:10]  # Limit results

    async def search_public_usernames(self, keywords, found_groups):
        """Search for groups with public usernames"""
        # Common username patterns for crypto groups
        username_patterns = []
        for keyword in keywords:
            username_patterns.extend([
                f"{keyword}group",
                f"{keyword}chat",
                f"{keyword}community",
                f"{keyword}official",
                f"{keyword}pump",
                f"{keyword}shill",
                f"{keyword}gems",
                f"{keyword}calls",
                f"{keyword}signals"
            ])

        # Add some known crypto group patterns
        crypto_patterns = [
            "cryptopump",
            "cryptogems",
            "solanagems",
            "pumpfun",
            "degengroup",
            "moonshots",
            "shillgroup",
            "cryptoshills",
            "gemhunters",
            "altcoins",
            "memecoin",
            "bscgems"
        ]
        username_patterns.extend(crypto_patterns)

        for pattern in username_patterns[:5]:  # Limit searches
            try:
                self.monitor_log(f"🔍 Checking username: @{pattern}")

                try:
                    entity = await self.client.get_entity(pattern)

                    if hasattr(entity, 'megagroup') or hasattr(entity, 'broadcast'):
                        # Check if we're already a member
                        try:
                            participants = await self.client.get_participants(entity, limit=1)
                            is_member = True
                        except:
                            is_member = False

                        if not is_member:
                            member_count = getattr(entity, 'participants_count', 0)
                            quality = min(100, max(50, (member_count / 1000) * 40 + 50))

                            found_groups.append({
                                'name': entity.title,
                                'members': str(member_count),
                                'type': 'Supergroup' if getattr(entity, 'megagroup', False) else 'Channel',
                                'keywords': pattern,
                                'quality': quality,
                                'source': 'username_search',
                                'entity': entity,
                                'username': pattern,
                                'invite_link': f"t.me/{pattern}"
                            })

                            self.monitor_log(f"📋 Found public group: @{pattern}")

                except Exception:
                    # Username doesn't exist, continue
                    pass

                await asyncio.sleep(2)  # Delay between username checks

            except Exception as e:
                continue

    async def search_invite_links_in_groups(self, keywords, found_groups):
        """Search for invite links in your existing groups"""
        self.monitor_log("🔍 Searching for invite links in your groups...")

        try:
            # Look through recent messages in your groups for invite links
            async for dialog in self.client.iter_dialogs(limit=20):
                if not (dialog.is_group or dialog.is_channel):
                    continue

                try:
                    # Get recent messages
                    messages = await self.client.get_messages(dialog.entity, limit=50)

                    for message in messages:
                        if not message.text:
                            continue

                        # Look for invite links
                        text = message.text.lower()
                        if 't.me/' in text or 'telegram.me/' in text:
                            # Extract invite links
                            import re
                            invite_patterns = [
                                r't\.me/\+([A-Za-z0-9_-]+)',
                                r't\.me/joinchat/([A-Za-z0-9_-]+)',
                                r'telegram\.me/joinchat/([A-Za-z0-9_-]+)',
                                r't\.me/([A-Za-z0-9_]+)'
                            ]

                            for pattern in invite_patterns:
                                matches = re.findall(pattern, text)
                                for match in matches:
                                    # Check if this link contains our keywords
                                    if any(keyword.lower() in text for keyword in keywords):
                                        invite_link = f"t.me/{match}" if not match.startswith('+') else f"t.me/+{match}"

                                        found_groups.append({
                                            'name': f"Group from invite: {match[:10]}...",
                                            'members': 'Unknown',
                                            'type': 'Invite Link',
                                            'keywords': ', '.join([k for k in keywords if k.lower() in text]),
                                            'quality': 60,
                                            'source': 'invite_link',
                                            'entity': None,
                                            'username': None,
                                            'invite_link': invite_link
                                        })

                                        self.monitor_log(f"🔗 Found invite link: {invite_link}")

                except Exception as e:
                    continue

                # Limit to avoid taking too long
                if len(found_groups) > 15:
                    break

        except Exception as e:
            self.monitor_log(f"❌ Invite link search error: {str(e)}")

    def process_discovered_group(self, group):
        """Process a discovered group"""
        # Check if group already exists
        existing = False
        for item in self.discovered_tree.get_children():
            if self.discovered_tree.item(item)['values'][0] == group['name']:
                existing = True
                break

        if not existing:
            # Add to discovered groups list
            self.root.after(0, lambda: self.discovered_tree.insert('', tk.END, values=(
                group['name'],
                group['members'],
                group['type'],
                group['keywords'],
                'Discovered',
                f"{group['quality']:.0f}%"
            )))

            # Auto-join if enabled and quality is good
            if self.auto_join_discovered_group(group):
                self.monitor_log(f"🤝 Auto-joining initiated for: {group['name']}")

                # Add to group categories
                if "🔍 Discovered" not in self.group_categories:
                    self.group_categories["🔍 Discovered"] = []

                self.group_categories["🔍 Discovered"].append({
                    'name': group['name'],
                    'type': group['type'],
                    'status': 'Active',
                    'last_used': 'Never',
                    'quality': group['quality']
                })

                self.root.after(0, self.update_category_list)

    def auto_discover_groups(self):
        """Auto-discovery called from automation worker"""
        if not self.discovery_running:
            # Run a quick discovery check
            self.monitor_log("🔍 Running auto-discovery check...")

            # Quick search for new groups
            keywords = self.discovery_keywords[:3]  # Use first 3 keywords
            found = self.search_similar_groups(keywords)

            if found:
                self.monitor_log(f"🔍 Auto-discovery found {len(found)} potential groups")
                for group in found:
                    self.process_discovered_group(group)

    def manual_search(self):
        """Manual search for groups"""
        keywords_text = self.keywords_text.get(1.0, tk.END).strip()
        keywords = [k.strip() for k in keywords_text.split(',') if k.strip()]

        if not keywords:
            messagebox.showerror("Error", "Please enter search keywords")
            return

        self.monitor_log(f"🔍 Manual search started with keywords: {', '.join(keywords)}")

        # Run discovery in thread to avoid blocking GUI
        import threading
        threading.Thread(target=self.run_discovery_cycle, daemon=True).start()

    def join_selected(self):
        """Join selected discovered groups"""
        selection = self.discovered_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select groups to join")
            return

        if not self.is_connected:
            messagebox.showerror("Error", "Please connect to Telegram first")
            return

        # Start joining in background thread
        import threading
        threading.Thread(target=self.join_groups_thread, args=(selection,), daemon=True).start()

    def join_groups_thread(self, selection):
        """Join groups in background thread"""
        joined = 0
        failed = 0

        for item in selection:
            try:
                group_name = self.discovered_tree.item(item)['values'][0]
                self.monitor_log(f"🤝 Attempting to join group: {group_name}")

                # Update status to "Joining"
                values = list(self.discovered_tree.item(item)['values'])
                values[4] = 'Joining...'
                self.root.after(0, lambda i=item, v=values: self.discovered_tree.item(i, values=v))

                # Attempt to join the group
                success = asyncio.run_coroutine_threadsafe(
                    self.join_group_async(group_name),
                    self.event_loop
                ).result(timeout=30)

                if success:
                    values[4] = 'Joined'
                    joined += 1
                    self.monitor_log(f"✅ Successfully joined: {group_name}")
                else:
                    values[4] = 'Failed'
                    failed += 1
                    self.monitor_log(f"❌ Failed to join: {group_name}")

                self.root.after(0, lambda i=item, v=values: self.discovered_tree.item(i, values=v))

                # Delay between joins to avoid rate limiting
                import time
                time.sleep(3)

            except Exception as e:
                self.monitor_log(f"❌ Error joining {group_name}: {str(e)}")
                values = list(self.discovered_tree.item(item)['values'])
                values[4] = 'Error'
                self.root.after(0, lambda i=item, v=values: self.discovered_tree.item(i, values=v))
                failed += 1

        # Show results
        result_msg = f"Group joining complete!\n✅ Joined: {joined}\n❌ Failed: {failed}"
        self.root.after(0, lambda: messagebox.showinfo("Join Results", result_msg))
        self.log_message(f"🤝 Group joining complete - Joined: {joined}, Failed: {failed}")

    async def join_group_async(self, group_info):
        """Join a group using real group information"""
        try:
            # If it's a discovered group with entity, use that first
            if isinstance(group_info, dict):
                if group_info.get('entity'):
                    return await self.join_by_entity(group_info['entity'], group_info['name'])
                elif group_info.get('invite_link'):
                    return await self.join_by_invite_link(group_info['invite_link'])
                elif group_info.get('username'):
                    return await self.join_by_username(group_info['username'])
                else:
                    group_identifier = group_info.get('name', '')
            else:
                group_identifier = group_info

            # If it's a string identifier
            if isinstance(group_identifier, str):
                # Method 1: Try to join by username if it looks like one
                if group_identifier.startswith('@') or ('/' not in group_identifier and 't.me' not in group_identifier):
                    return await self.join_by_username(group_identifier)

                # Method 2: Try to join by invite link
                elif 't.me/' in group_identifier or 'telegram.me/' in group_identifier:
                    return await self.join_by_invite_link(group_identifier)

                # Method 3: Try to search and join
                else:
                    return await self.search_and_join(group_identifier)

            return False

        except Exception as e:
            self.monitor_log(f"❌ Join error: {str(e)}")
            return False

    async def join_by_entity(self, entity, group_name):
        """Join group using Telegram entity"""
        try:
            self.monitor_log(f"🤝 Attempting to join: {group_name}")

            # Check if it's a channel or supergroup
            if hasattr(entity, 'broadcast') and entity.broadcast:
                # It's a channel
                await self.client(JoinChannelRequest(entity))
                self.monitor_log(f"✅ Joined channel: {group_name}")
                return True
            elif hasattr(entity, 'megagroup') and entity.megagroup:
                # It's a supergroup
                await self.client(JoinChannelRequest(entity))
                self.monitor_log(f"✅ Joined supergroup: {group_name}")
                return True
            elif hasattr(entity, 'username') and entity.username:
                # Try joining by username
                await self.client(JoinChannelRequest(entity))
                self.monitor_log(f"✅ Joined via username: {group_name}")
                return True
            else:
                # Try to get invite link and join
                try:
                    # Get full entity info
                    full_entity = await self.client.get_entity(entity)
                    await self.client(JoinChannelRequest(full_entity))
                    self.monitor_log(f"✅ Joined via full entity: {group_name}")
                    return True
                except Exception as e2:
                    self.monitor_log(f"❌ Cannot join {group_name}: Not a public group or channel")
                    return False

        except UserAlreadyParticipantError:
            self.monitor_log(f"ℹ️ Already a member of: {group_name}")
            return True
        except FloodWaitError as e:
            self.monitor_log(f"⏳ Rate limited for {e.seconds} seconds when joining: {group_name}")
            return False
        except Exception as e:
            error_msg = str(e).lower()
            if "invite" in error_msg or "private" in error_msg:
                self.monitor_log(f"🔒 {group_name} requires invite link (private group)")
                return False
            elif "banned" in error_msg:
                self.monitor_log(f"🚫 Banned from joining: {group_name}")
                return False
            else:
                self.monitor_log(f"❌ Failed to join {group_name}: {str(e)}")
                return False

    async def join_by_username(self, username):
        """Join group by username"""
        try:
            # Clean username
            if username.startswith('@'):
                username = username[1:]

            self.monitor_log(f"🔍 Searching for group: @{username}")

            # Get entity
            entity = await self.client.get_entity(username)

            # Join the group/channel
            if hasattr(entity, 'megagroup') or hasattr(entity, 'broadcast'):
                await self.client(JoinChannelRequest(entity))
                self.monitor_log(f"✅ Joined channel/supergroup: @{username}")
            else:
                self.monitor_log(f"ℹ️ Already in group or it's a user: @{username}")

            return True

        except UserAlreadyParticipantError:
            self.monitor_log(f"ℹ️ Already a member of: @{username}")
            return True
        except Exception as e:
            self.monitor_log(f"❌ Failed to join @{username}: {str(e)}")
            return False

    async def join_by_invite_link(self, invite_link):
        """Join group by invite link with automatic 'Join' button handling"""
        try:
            # Extract invite hash from link
            if 't.me/+' in invite_link:
                invite_hash = invite_link.split('t.me/+')[1]
            elif 't.me/joinchat/' in invite_link:
                invite_hash = invite_link.split('t.me/joinchat/')[1]
            elif 'telegram.me/joinchat/' in invite_link:
                invite_hash = invite_link.split('telegram.me/joinchat/')[1]
            else:
                self.monitor_log(f"❌ Invalid invite link format: {invite_link}")
                return False

            # Clean hash (remove any extra parameters)
            invite_hash = invite_hash.split('?')[0].split('&')[0]

            self.monitor_log(f"🔍 Processing invite link with hash: {invite_hash[:10]}...")

            # Step 1: Check the invite first
            try:
                invite_info = await self.client(CheckChatInviteRequest(invite_hash))

                if isinstance(invite_info, ChatInviteAlready):
                    self.monitor_log(f"ℹ️ Already a member of this group")
                    return True
                elif isinstance(invite_info, ChatInvite):
                    self.monitor_log(f"📋 Group info: {invite_info.title} ({invite_info.participants_count} members)")

                    # Check if group allows joining
                    if hasattr(invite_info, 'request_needed') and invite_info.request_needed:
                        self.monitor_log(f"⚠️ Group requires admin approval")
                        # Still try to join - will send join request

            except Exception as e:
                self.monitor_log(f"⚠️ Could not check invite info: {str(e)}")

            # Step 2: Join the group (this handles the "Join" button automatically)
            try:
                result = await self.client(ImportChatInviteRequest(invite_hash))

                if result:
                    self.monitor_log(f"✅ Successfully joined group via invite link")

                    # Add small delay to ensure join is processed
                    await asyncio.sleep(2)

                    return True
                else:
                    self.monitor_log(f"❌ Join request returned no result")
                    return False

            except UserAlreadyParticipantError:
                self.monitor_log(f"ℹ️ Already a member of this group")
                return True
            except InviteHashExpiredError:
                self.monitor_log(f"❌ Invite link has expired")
                return False
            except InviteHashInvalidError:
                self.monitor_log(f"❌ Invalid invite link")
                return False
            except FloodWaitError as e:
                self.monitor_log(f"⏳ Rate limited, need to wait {e.seconds} seconds")
                await asyncio.sleep(e.seconds)
                # Retry once after waiting
                try:
                    result = await self.client(ImportChatInviteRequest(invite_hash))
                    return result is not None
                except:
                    return False
            except Exception as e:
                self.monitor_log(f"❌ Failed to join via invite: {str(e)}")
                return False

        except Exception as e:
            self.monitor_log(f"❌ Error processing invite link: {str(e)}")
            return False

    async def search_and_join(self, group_name):
        """Search for group by name and join"""
        try:
            self.monitor_log(f"🔍 Searching for group: {group_name}")

            # Search for the group
            async for dialog in self.client.iter_dialogs():
                if group_name.lower() in dialog.name.lower():
                    if hasattr(dialog.entity, 'megagroup') or hasattr(dialog.entity, 'broadcast'):
                        try:
                            await self.client(JoinChannelRequest(dialog.entity))
                            self.monitor_log(f"✅ Found and joined: {dialog.name}")
                            return True
                        except UserAlreadyParticipantError:
                            self.monitor_log(f"ℹ️ Already a member of: {dialog.name}")
                            return True
                        except Exception as e:
                            self.monitor_log(f"❌ Failed to join {dialog.name}: {str(e)}")
                            continue

            self.monitor_log(f"❌ Could not find group: {group_name}")
            return False

        except Exception as e:
            self.monitor_log(f"❌ Search error: {str(e)}")
            return False

    def auto_join_discovered_group(self, group_info):
        """Auto-join a discovered group if quality is high enough"""
        if not self.auto_join_discovered.get():
            return False

        if group_info.get('quality', 0) < 70:
            self.monitor_log(f"⚠️ Group quality too low for auto-join: {group_info['name']} ({group_info['quality']}%)")
            return False

        self.monitor_log(f"🤝 Auto-joining high-quality group: {group_info['name']}")

        # Start join process in background
        import threading
        threading.Thread(
            target=lambda: asyncio.run_coroutine_threadsafe(
                self.join_group_async(group_info['name']),
                self.event_loop
            ),
            daemon=True
        ).start()

        return True

    def ignore_selected(self):
        """Ignore selected discovered groups"""
        selection = self.discovered_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select groups to ignore")
            return

        for item in selection:
            values = list(self.discovered_tree.item(item)['values'])
            values[4] = 'Ignored'
            self.discovered_tree.item(item, values=values)

        self.log_message(f"❌ Ignored {len(selection)} groups")

    def clear_discovered(self):
        """Clear discovered groups list"""
        for item in self.discovered_tree.get_children():
            self.discovered_tree.delete(item)

        self.log_message("🗑️ Cleared discovered groups list")
        self.monitor_log("🗑️ Discovered groups list cleared")

    def update_discovery_stats(self):
        """Update discovery statistics"""
        total_found = len(self.discovered_tree.get_children())
        joined_count = 0
        quality_sum = 0

        for item in self.discovered_tree.get_children():
            values = self.discovered_tree.item(item)['values']
            if values[4] == 'Joined':
                joined_count += 1

            try:
                quality = float(values[5].replace('%', ''))
                quality_sum += quality
            except:
                pass

        avg_quality = quality_sum / total_found if total_found > 0 else 0

        self.disc_stats_found.config(text=f"Groups Found: {total_found}")
        self.disc_stats_joined.config(text=f"Groups Joined: {joined_count}")
        self.disc_stats_quality.config(text=f"Avg Quality: {avg_quality:.0f}%")
    
    def send_bulk_messages(self):
        """Send bulk messages"""
        selected_indices = self.groups_listbox.curselection()
        if not selected_indices:
            messagebox.showerror("Error", "Please select at least one group")
            return

        message = self.message_text.get(1.0, tk.END).strip()
        msg_type = self.msg_type_var.get()

        if not message and msg_type != "photo":
            messagebox.showerror("Error", "Please enter a message")
            return

        if msg_type == "photo" and not self.selected_photo_path:
            messagebox.showerror("Error", "Please select a photo or change message type")
            return

        selected_groups = [self.groups[i] for i in selected_indices]

        # Create confirmation message
        confirm_msg = f"Send to {len(selected_groups)} groups?\n\n"

        if msg_type == "photo":
            import os
            photo_name = os.path.basename(self.selected_photo_path)
            confirm_msg += f"📷 Photo: {photo_name}\n"

        if message:
            confirm_msg += f"📝 Message: {message[:100]}{'...' if len(message) > 100 else ''}"

        result = messagebox.askyesno("Confirm Bulk Send", confirm_msg)
        if not result:
            return

        # Get delay
        delay = 1.0
        if self.rate_var.get() == "custom":
            try:
                delay = float(self.custom_delay.get())
            except:
                delay = 1.0
        else:
            delay = float(self.rate_var.get())

        # Disable send button
        self.send_btn.config(state='disabled', text='Sending...')
        self.progress_frame.pack(fill=tk.X, pady=5)

        # Prepare message data
        message_data = {
            'text': message,
            'type': msg_type,
            'photo_path': self.selected_photo_path if msg_type == "photo" else None
        }

        # Start sending in thread
        thread = threading.Thread(target=self.send_messages_thread,
                                 args=(selected_groups, message_data, delay))
        thread.daemon = True
        thread.start()

    def send_messages_thread(self, groups, message_data, delay):
        """Send messages in thread"""
        try:
            # Use the dedicated event loop
            future = asyncio.run_coroutine_threadsafe(self.send_messages_async(groups, message_data, delay), self.event_loop)
            future.result(timeout=600)  # Longer timeout for bulk sending with photos
        except Exception as e:
            self.log_message(f"Sending error: {str(e)}")
        finally:
            self.root.after(0, lambda: self.send_btn.config(state='normal', text='🚀 SEND BULK MESSAGES'))

    async def send_messages_async(self, groups, message_data, delay):
        """Send messages async with support for photos and rich content"""
        successful = 0
        failed = 0
        total = len(groups)

        message_text = message_data['text']
        message_type = message_data['type']
        photo_path = message_data['photo_path']

        self.root.after(0, lambda: self.progress_bar.config(maximum=total, value=0))

        for i, group in enumerate(groups, 1):
            try:
                self.root.after(0, lambda i=i, name=group['name']:
                               self.progress_label.config(text=f"Sending to {name} ({i}/{total})"))

                # Send based on message type
                if message_type == "photo" and photo_path:
                    # Send photo with caption
                    await self.client.send_file(
                        group['entity'],
                        photo_path,
                        caption=message_text if message_text else None
                    )
                    self.log_message(f"✅ Sent photo to {group['name']}")
                else:
                    # Send text message (with link preview if applicable)
                    await self.client.send_message(group['entity'], message_text)
                    self.log_message(f"✅ Sent message to {group['name']}")

                successful += 1

                # Update progress
                self.root.after(0, lambda i=i: self.progress_bar.config(value=i))

                # Rate limiting (longer delay for photos)
                if i < total:
                    photo_delay = delay * 1.5 if message_type == "photo" else delay
                    await asyncio.sleep(photo_delay)

            except FloodWaitError as e:
                self.log_message(f"⏳ Rate limited, waiting {e.seconds} seconds...")
                await asyncio.sleep(e.seconds)
                # Retry
                try:
                    if message_type == "photo" and photo_path:
                        await self.client.send_file(
                            group['entity'],
                            photo_path,
                            caption=message_text if message_text else None
                        )
                    else:
                        await self.client.send_message(group['entity'], message_text)
                    successful += 1
                    self.log_message(f"✅ Sent to {group['name']} (retry)")
                except:
                    failed += 1
                    self.log_message(f"❌ Failed to send to {group['name']} (retry failed)")

            except (PeerFloodError, UserPrivacyRestrictedError):
                failed += 1
                self.log_message(f"❌ Privacy/flood error for {group['name']}")

            except Exception as e:
                failed += 1
                self.log_message(f"❌ Error sending to {group['name']}: {str(e)}")

        # Show results
        success_rate = (successful/(successful+failed)*100) if (successful+failed) > 0 else 0
        content_type = "photos" if message_type == "photo" else "messages"
        result_msg = f"""📊 BULK MESSAGING RESULTS
📤 Content: {content_type.title()}
✅ Successful: {successful}
❌ Failed: {failed}
📈 Success rate: {success_rate:.1f}%"""

        self.log_message(result_msg)
        self.root.after(0, lambda: self.progress_label.config(text="Bulk messaging completed!"))
        self.root.after(0, lambda: messagebox.showinfo("Results", result_msg))

        # Record campaign analytics
        from datetime import datetime
        campaign_record = {
            'date': datetime.now().strftime("%Y-%m-%d %H:%M"),
            'groups_targeted': total,
            'messages_sent': successful,
            'success_rate': success_rate,
            'message_type': message_type.title(),
            'duration': 0,  # Could be calculated
            'failed_count': failed
        }
        self.campaign_history.append(campaign_record)

        # Update analytics display
        self.root.after(0, self.update_campaign_tree)

    def run(self):
        """Run the GUI application"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("Application stopped by user")
        except Exception as e:
            self.log_message(f"Unexpected error: {str(e)}")
        finally:
            self.cleanup()

    def on_closing(self):
        """Handle window closing"""
        self.cleanup()
        self.root.destroy()

    def cleanup(self):
        """Cleanup resources"""
        try:
            if self.client and self.is_connected:
                # Disconnect client using the event loop
                future = asyncio.run_coroutine_threadsafe(self.client.disconnect(), self.event_loop)
                future.result(timeout=5)
                self.log_message("Disconnected from Telegram")
        except:
            pass

        try:
            # Stop the event loop
            if self.event_loop and not self.event_loop.is_closed():
                self.event_loop.call_soon_threadsafe(self.event_loop.stop)
        except:
            pass

# Legacy console methods (kept for compatibility but not used in GUI)
class TelegramDMBot:
    def __init__(self):
        pass

    def display_contacts(self, contacts, show_all=True):
        """Display contacts in a nice format"""
        print("\n" + "="*60)
        print("📱 YOUR CONTACTS & CHATS")
        print("="*60)

        users = [c for c in contacts if c['type'] == 'User']
        groups = [c for c in contacts if c['type'] in ['Group', 'Supergroup']]
        channels = [c for c in contacts if c['type'] == 'Channel']
        bots = [c for c in contacts if c['type'] == 'Bot']

        if users and show_all:
            print(f"\n👥 USERS ({len(users)}):")
            if len(users) <= 50:
                for i, contact in enumerate(users, 1):
                    print(f"  {i:2d}. {contact['name']}")
            else:
                for i, contact in enumerate(users[:25], 1):
                    print(f"  {i:2d}. {contact['name']}")
                print(f"  ... and {len(users)-25} more users")

        if groups:
            print(f"\n👨‍👩‍👧‍👦 GROUPS ({len(groups)}):")
            start_idx = len(users) + 1 if show_all else 1
            if len(groups) <= 50:
                for i, contact in enumerate(groups, start_idx):
                    print(f"  {i:2d}. {contact['name']} ({contact['type']})")
            else:
                for i, contact in enumerate(groups[:25], start_idx):
                    print(f"  {i:2d}. {contact['name']} ({contact['type']})")
                print(f"  ... and {len(groups)-25} more groups")

        if channels and show_all:
            print(f"\n📢 CHANNELS ({len(channels)}):")
            start_idx = len(users) + len(groups) + 1
            if len(channels) <= 50:
                for i, contact in enumerate(channels, start_idx):
                    print(f"  {i:2d}. {contact['name']}")
            else:
                for i, contact in enumerate(channels[:25], start_idx):
                    print(f"  {i:2d}. {contact['name']}")
                print(f"  ... and {len(channels)-25} more channels")

        if bots and show_all:
            print(f"\n🤖 BOTS ({len(bots)}):")
            start_idx = len(users) + len(groups) + len(channels) + 1
            if len(bots) <= 50:
                for i, contact in enumerate(bots, start_idx):
                    print(f"  {i:2d}. {contact['name']}")
            else:
                for i, contact in enumerate(bots[:25], start_idx):
                    print(f"  {i:2d}. {contact['name']}")
                print(f"  ... and {len(bots)-25} more bots")

        print("="*60)
    
    def select_recipients(self, contacts):
        """Select recipients for messaging"""
        recipients = []
        
        print("\n📝 SELECT RECIPIENTS:")
        print("Enter numbers separated by commas (e.g., 1,3,5)")
        print("Or enter 'all' to select all contacts")
        print("Or enter 'range' for a range (e.g., 1-10)")
        
        while True:
            selection = input("\nYour selection: ").strip().lower()
            
            if selection == 'all':
                recipients = contacts.copy()
                break
            elif '-' in selection and selection.replace('-', '').isdigit():
                try:
                    start, end = map(int, selection.split('-'))
                    if 1 <= start <= len(contacts) and 1 <= end <= len(contacts):
                        recipients = contacts[start-1:end]
                        break
                    else:
                        print("❌ Range out of bounds!")
                except:
                    print("❌ Invalid range format!")
            else:
                try:
                    indices = [int(x.strip()) for x in selection.split(',')]
                    if all(1 <= i <= len(contacts) for i in indices):
                        recipients = [contacts[i-1] for i in indices]
                        break
                    else:
                        print("❌ Some numbers are out of range!")
                except:
                    print("❌ Invalid selection format!")
        
        print(f"\n✅ Selected {len(recipients)} recipients:")
        for recipient in recipients:
            print(f"  • {recipient['name']} ({recipient['type']})")
        
        return recipients
    
    def compose_message(self):
        """Compose message to send"""
        print("\n✍️  COMPOSE YOUR MESSAGE:")
        print("(Press Enter twice to finish, or type 'cancel' to abort)")
        
        lines = []
        empty_lines = 0
        
        while True:
            line = input()
            if line.lower() == 'cancel':
                return None
            
            if line == '':
                empty_lines += 1
                if empty_lines >= 2:
                    break
            else:
                empty_lines = 0
            
            lines.append(line)
        
        # Remove trailing empty lines
        while lines and lines[-1] == '':
            lines.pop()
        
        message = '\n'.join(lines)
        
        if not message.strip():
            print("❌ Message cannot be empty!")
            return None
        
        print(f"\n📝 Your message ({len(message)} characters):")
        print("-" * 40)
        print(message)
        print("-" * 40)
        
        confirm = input("\nSend this message? (y/n): ").strip().lower()
        return message if confirm == 'y' else None
    
    async def send_messages(self, recipients, message, delay=1):
        """Send messages to selected recipients"""
        print(f"\n🚀 Sending message to {len(recipients)} recipients...")

        successful = 0
        failed = 0
        errors = []

        for i, recipient in enumerate(recipients, 1):
            try:
                print(f"[{i}/{len(recipients)}] Sending to {recipient['name']}...", end=' ')

                await self.client.send_message(recipient['entity'], message)
                print("✅")
                successful += 1

                # Rate limiting - wait between messages
                if i < len(recipients):
                    await asyncio.sleep(delay)

            except FloodWaitError as e:
                print(f"⏳ Rate limited, waiting {e.seconds} seconds...")
                await asyncio.sleep(e.seconds)
                # Retry
                try:
                    await self.client.send_message(recipient['entity'], message)
                    print("✅ (retry successful)")
                    successful += 1
                except Exception as retry_error:
                    print("❌ (retry failed)")
                    failed += 1
                    errors.append(f"{recipient['name']}: {str(retry_error)}")

            except (PeerFloodError, UserPrivacyRestrictedError) as e:
                print("❌ (privacy/flood error)")
                failed += 1
                errors.append(f"{recipient['name']}: {str(e)}")

            except Exception as e:
                print(f"❌ ({str(e)})")
                failed += 1
                errors.append(f"{recipient['name']}: {str(e)}")

        print(f"\n📊 RESULTS:")
        print(f"✅ Successful: {successful}")
        print(f"❌ Failed: {failed}")
        if successful + failed > 0:
            print(f"📈 Success rate: {successful/(successful+failed)*100:.1f}%")

        # Show first few errors if any
        if errors and len(errors) <= 5:
            print(f"\n❌ Errors:")
            for error in errors:
                print(f"  • {error}")
        elif errors:
            print(f"\n❌ First 5 errors (total: {len(errors)}):")
            for error in errors[:5]:
                print(f"  • {error}")

    async def send_to_all_groups(self):
        """Send message to all groups with bulk options"""
        contacts = await self.get_contacts()
        if not contacts:
            print("❌ No contacts found!")
            return

        groups = [c for c in contacts if c['type'] in ['Group', 'Supergroup']]
        if not groups:
            print("❌ No groups found!")
            return

        print(f"\n👨‍👩‍👧‍👦 Found {len(groups)} groups")

        # Show groups with pagination
        self.display_contacts(contacts, show_all=False)

        print(f"\n📤 BULK GROUP MESSAGING OPTIONS:")
        print("1. 🎯 Send to ALL groups")
        print("2. 📝 Select specific groups")
        print("3. 📊 Send to groups by range")
        print("4. 🔍 Search and select groups")
        print("5. 🔙 Back to main menu")

        choice = input("\nSelect option (1-5): ").strip()

        if choice == '1':
            # Send to all groups
            print(f"\n⚠️  You are about to send a message to ALL {len(groups)} groups!")
            confirm = input("Are you sure? Type 'YES' to confirm: ").strip()
            if confirm != 'YES':
                print("❌ Operation cancelled!")
                return

            message = self.compose_message()
            if not message:
                return

            # Ask for delay between messages
            print(f"\n⏱️  RATE LIMITING:")
            print("1. Fast (0.5s delay) - Risk of rate limiting")
            print("2. Normal (1s delay) - Recommended")
            print("3. Safe (2s delay) - Very safe")
            print("4. Custom delay")

            delay_choice = input("Select delay option (1-4): ").strip()
            delay = 1  # default

            if delay_choice == '1':
                delay = 0.5
            elif delay_choice == '2':
                delay = 1
            elif delay_choice == '3':
                delay = 2
            elif delay_choice == '4':
                try:
                    delay = float(input("Enter delay in seconds: "))
                except:
                    delay = 1

            await self.send_messages(groups, message, delay)

        elif choice == '2':
            # Select specific groups
            recipients = self.select_recipients(groups)
            if not recipients:
                return

            message = self.compose_message()
            if not message:
                return

            await self.send_messages(recipients, message)

        elif choice == '3':
            # Send by range
            print(f"\nEnter range (1-{len(groups)}):")
            try:
                start = int(input("Start index: "))
                end = int(input("End index: "))

                if 1 <= start <= len(groups) and 1 <= end <= len(groups) and start <= end:
                    selected_groups = groups[start-1:end]
                    print(f"\n✅ Selected {len(selected_groups)} groups (#{start} to #{end})")

                    message = self.compose_message()
                    if not message:
                        return

                    await self.send_messages(selected_groups, message)
                else:
                    print("❌ Invalid range!")
            except ValueError:
                print("❌ Invalid input!")

        elif choice == '4':
            # Search groups
            search_term = input("\nEnter search term: ").strip().lower()
            if not search_term:
                print("❌ Search term cannot be empty!")
                return

            matching_groups = [g for g in groups if search_term in g['name'].lower()]

            if not matching_groups:
                print(f"❌ No groups found matching '{search_term}'")
                return

            print(f"\n🔍 Found {len(matching_groups)} groups matching '{search_term}':")
            for i, group in enumerate(matching_groups, 1):
                print(f"  {i:2d}. {group['name']} ({group['type']})")

            print(f"\nSend to all {len(matching_groups)} matching groups?")
            confirm = input("Type 'yes' to confirm: ").strip().lower()

            if confirm == 'yes':
                message = self.compose_message()
                if not message:
                    return

                await self.send_messages(matching_groups, message)
            else:
                print("❌ Operation cancelled!")

        elif choice == '5':
            return
        else:
            print("❌ Invalid option!")
    
    async def main_menu(self):
        """Main application menu"""
        while True:
            print("\n" + "="*60)
            print("🚀 TELEGRAM DM BOT - BULK MESSAGING")
            print("="*60)
            print("1. 📤 Send messages to selected contacts")
            print("2. 👨‍👩‍👧‍👦 Bulk send to groups (FAST)")
            print("3. 📊 View contact statistics")
            print("4. 🔄 Refresh contacts")
            print("5. 🚪 Exit")
            print("="*60)

            choice = input("Select option (1-5): ").strip()

            if choice == '1':
                contacts = await self.get_contacts()
                if not contacts:
                    print("❌ No contacts found!")
                    continue

                self.display_contacts(contacts)
                recipients = self.select_recipients(contacts)

                if not recipients:
                    print("❌ No recipients selected!")
                    continue

                message = self.compose_message()
                if not message:
                    print("❌ Message composition cancelled!")
                    continue

                await self.send_messages(recipients, message)

            elif choice == '2':
                await self.send_to_all_groups()

            elif choice == '3':
                contacts = await self.get_contacts()
                if contacts:
                    self.show_statistics(contacts)

            elif choice == '4':
                await self.get_contacts()

            elif choice == '5':
                print("👋 Goodbye!")
                break

            else:
                print("❌ Invalid option!")

    def show_statistics(self, contacts):
        """Show contact statistics"""
        users = [c for c in contacts if c['type'] == 'User']
        groups = [c for c in contacts if c['type'] in ['Group', 'Supergroup']]
        channels = [c for c in contacts if c['type'] == 'Channel']
        bots = [c for c in contacts if c['type'] == 'Bot']

        print("\n" + "="*50)
        print("📊 CONTACT STATISTICS")
        print("="*50)
        print(f"👥 Users: {len(users)}")
        print(f"👨‍👩‍👧‍👦 Groups: {len(groups)}")
        print(f"📢 Channels: {len(channels)}")
        print(f"🤖 Bots: {len(bots)}")
        print(f"📱 Total: {len(contacts)}")
        print("="*50)

        if groups:
            print(f"\n🎯 BULK MESSAGING POTENTIAL:")
            print(f"   • You can send to {len(groups)} groups at once")
            print(f"   • Estimated time (1s delay): {len(groups)} seconds")
            print(f"   • Estimated time (2s delay): {len(groups)*2} seconds")
    
    def show_welcome(self):
        """Show welcome screen"""
        # Clear screen for Windows
        os.system('cls' if os.name == 'nt' else 'clear')

        print("\n" + "="*70)
        print("🚀 TELEGRAM DM BOT - BULK MESSAGING TOOL")
        print("="*70)
        print("📱 Send messages to 1000+ groups efficiently!")
        print("🎯 Perfect for bulk messaging, announcements, and marketing")
        print("⚡ Fast, safe, and user-friendly")
        print("="*70)

        print("\n🔧 READY TO START:")
        print("✅ API credentials configured (PumpX Bot)")
        print("📱 Just login with your Telegram account")
        print("🚀 Start bulk messaging immediately!")

        print("\n📋 WHAT YOU'LL NEED:")
        print("• Your Telegram phone number")
        print("• Access to your phone for verification code")

        print("\n💻 WINDOWS DESKTOP APP VERSION")
        print("📦 Standalone executable - no Python required!")

        print("\n" + "="*70)
        input("Press Enter to login and start...")

    async def run(self):
        """Run the bot"""
        try:
            self.show_welcome()

            if await self.connect():
                try:
                    await self.main_menu()
                finally:
                    if self.client:
                        await self.client.disconnect()
                        print("📱 Disconnected from Telegram")
            else:
                print("❌ Failed to start bot")
        except KeyboardInterrupt:
            print("\n\n👋 Bot stopped by user")
        except Exception as e:
            print(f"\n❌ Unexpected error: {str(e)}")
        finally:
            print("\n🔚 Press Enter to exit...")
            input()

if __name__ == "__main__":
    # Create and run GUI application
    app = TelegramDMBotGUI()
    app.run()
