#!/usr/bin/env python3
"""
Telegram DM Bot - Send messages to users/groups via console interface
"""
import asyncio
import os
import sys
import webbrowser
from typing import List, Dict, Optional
from telethon import TelegramClient
from telethon.tl.types import User, <PERSON><PERSON>, Channel
from telethon.errors import FloodWaitError, PeerFloodError, UserPrivacyRestrictedError
import time

class TelegramDMBot:
    def __init__(self):
        self.client = None
        self.api_id = None
        self.api_hash = None
        self.session_name = "telegram_dm_session"
        
    def setup_credentials(self):
        """Setup Telegram API credentials with guided application creation"""
        print("🔧 Setting up Telegram API credentials...")
        print("=" * 60)
        print("📱 TELEGRAM API APPLICATION SETUP")
        print("=" * 60)

        print("\n🚀 STEP 1: Create Telegram Application")
        print("We'll guide you through creating a new Telegram app to get API credentials.")
        print()

        # Guide user through application creation
        print("📝 Opening Telegram API page: https://my.telegram.org/apps")

        try:
            webbrowser.open("https://my.telegram.org/apps")
            print("✅ Browser opened automatically")
        except:
            print("❌ Could not open browser automatically")
            print("📝 Please manually go to: https://my.telegram.org/apps")

        print("🔐 Login with your Telegram account (phone number + verification code)")
        print()

        input("Press Enter when you're logged in and ready to create the app...")

        print("\n📋 STEP 2: Fill Application Form")
        print("=" * 40)

        # Get app details from user
        print("📱 App title: (e.g., 'My DM Bot', 'Bulk Messenger')")
        app_title = input("Enter app title: ").strip()
        if not app_title:
            app_title = "Telegram DM Bot"

        print(f"\n📝 Short name: (5-32 alphanumeric characters)")
        print("Examples: 'mydmbot', 'bulkmsg', 'telebot123'")
        short_name = input("Enter short name: ").strip()
        while not short_name or len(short_name) < 5 or len(short_name) > 32:
            print("❌ Short name must be 5-32 alphanumeric characters")
            short_name = input("Enter short name: ").strip()

        print(f"\n🌐 URL: (optional, can leave empty)")
        url = input("Enter URL (or press Enter to skip): ").strip()

        print(f"\n💻 Platform: Select 'Desktop' (recommended for this bot)")

        print(f"\n📄 Description:")
        description = input("Enter description (or press Enter for default): ").strip()
        if not description:
            description = "Telegram DM Bot for bulk messaging"

        print("\n" + "=" * 60)
        print("📋 FILL THE FORM WITH THESE VALUES:")
        print("=" * 60)
        print(f"App title: {app_title}")
        print(f"Short name: {short_name}")
        print(f"URL: {url if url else '(leave empty)'}")
        print(f"Platform: Desktop")
        print(f"Description: {description}")
        print("=" * 60)

        input("\nPress Enter after you've filled the form and clicked 'Create application'...")

        print("\n🎉 STEP 3: Get Your Credentials")
        print("=" * 40)
        print("After creating the app, you'll see:")
        print("• API ID (a number)")
        print("• API Hash (a long string)")
        print()

        while not self.api_id:
            try:
                api_id_input = input("📋 Copy and paste your API ID: ").strip()
                self.api_id = int(api_id_input)
                print(f"✅ API ID saved: {self.api_id}")
            except ValueError:
                print("❌ Invalid API ID. Please enter the number you see on the page.")

        while not self.api_hash:
            self.api_hash = input("📋 Copy and paste your API Hash: ").strip()
            if not self.api_hash:
                print("❌ API Hash cannot be empty.")
            else:
                print(f"✅ API Hash saved: {self.api_hash[:10]}...")

        print("\n🎉 Credentials configured successfully!")
        print("💾 These will be saved in your session file for future use.")
        print()
    
    async def connect(self):
        """Connect to Telegram"""
        if not self.api_id or not self.api_hash:
            self.setup_credentials()
        
        try:
            self.client = TelegramClient(self.session_name, self.api_id, self.api_hash)
            print("🔄 Connecting to Telegram...")
            await self.client.start()
            
            me = await self.client.get_me()
            print(f"✅ Successfully logged in as: {me.first_name}")
            if me.username:
                print(f"   Username: @{me.username}")
            print()
            return True
            
        except Exception as e:
            print(f"❌ Failed to connect: {str(e)}")
            return False
    
    async def get_contacts(self):
        """Get all contacts and chats"""
        print("📋 Loading your contacts and chats...")
        contacts = []
        
        try:
            async for dialog in self.client.iter_dialogs():
                contact_type = "Unknown"
                if isinstance(dialog.entity, User):
                    if not dialog.entity.bot:
                        contact_type = "User"
                    else:
                        contact_type = "Bot"
                elif isinstance(dialog.entity, Chat):
                    contact_type = "Group"
                elif isinstance(dialog.entity, Channel):
                    contact_type = "Channel" if dialog.entity.broadcast else "Supergroup"
                
                contacts.append({
                    'id': dialog.entity.id,
                    'name': dialog.name or "Unknown",
                    'type': contact_type,
                    'entity': dialog.entity
                })
            
            print(f"✅ Loaded {len(contacts)} contacts/chats")
            return contacts
            
        except Exception as e:
            print(f"❌ Failed to load contacts: {str(e)}")
            return []
    
    def display_contacts(self, contacts, show_all=True):
        """Display contacts in a nice format"""
        print("\n" + "="*60)
        print("📱 YOUR CONTACTS & CHATS")
        print("="*60)

        users = [c for c in contacts if c['type'] == 'User']
        groups = [c for c in contacts if c['type'] in ['Group', 'Supergroup']]
        channels = [c for c in contacts if c['type'] == 'Channel']
        bots = [c for c in contacts if c['type'] == 'Bot']

        if users and show_all:
            print(f"\n👥 USERS ({len(users)}):")
            if len(users) <= 50:
                for i, contact in enumerate(users, 1):
                    print(f"  {i:2d}. {contact['name']}")
            else:
                for i, contact in enumerate(users[:25], 1):
                    print(f"  {i:2d}. {contact['name']}")
                print(f"  ... and {len(users)-25} more users")

        if groups:
            print(f"\n👨‍👩‍👧‍👦 GROUPS ({len(groups)}):")
            start_idx = len(users) + 1 if show_all else 1
            if len(groups) <= 50:
                for i, contact in enumerate(groups, start_idx):
                    print(f"  {i:2d}. {contact['name']} ({contact['type']})")
            else:
                for i, contact in enumerate(groups[:25], start_idx):
                    print(f"  {i:2d}. {contact['name']} ({contact['type']})")
                print(f"  ... and {len(groups)-25} more groups")

        if channels and show_all:
            print(f"\n📢 CHANNELS ({len(channels)}):")
            start_idx = len(users) + len(groups) + 1
            if len(channels) <= 50:
                for i, contact in enumerate(channels, start_idx):
                    print(f"  {i:2d}. {contact['name']}")
            else:
                for i, contact in enumerate(channels[:25], start_idx):
                    print(f"  {i:2d}. {contact['name']}")
                print(f"  ... and {len(channels)-25} more channels")

        if bots and show_all:
            print(f"\n🤖 BOTS ({len(bots)}):")
            start_idx = len(users) + len(groups) + len(channels) + 1
            if len(bots) <= 50:
                for i, contact in enumerate(bots, start_idx):
                    print(f"  {i:2d}. {contact['name']}")
            else:
                for i, contact in enumerate(bots[:25], start_idx):
                    print(f"  {i:2d}. {contact['name']}")
                print(f"  ... and {len(bots)-25} more bots")

        print("="*60)
    
    def select_recipients(self, contacts):
        """Select recipients for messaging"""
        recipients = []
        
        print("\n📝 SELECT RECIPIENTS:")
        print("Enter numbers separated by commas (e.g., 1,3,5)")
        print("Or enter 'all' to select all contacts")
        print("Or enter 'range' for a range (e.g., 1-10)")
        
        while True:
            selection = input("\nYour selection: ").strip().lower()
            
            if selection == 'all':
                recipients = contacts.copy()
                break
            elif '-' in selection and selection.replace('-', '').isdigit():
                try:
                    start, end = map(int, selection.split('-'))
                    if 1 <= start <= len(contacts) and 1 <= end <= len(contacts):
                        recipients = contacts[start-1:end]
                        break
                    else:
                        print("❌ Range out of bounds!")
                except:
                    print("❌ Invalid range format!")
            else:
                try:
                    indices = [int(x.strip()) for x in selection.split(',')]
                    if all(1 <= i <= len(contacts) for i in indices):
                        recipients = [contacts[i-1] for i in indices]
                        break
                    else:
                        print("❌ Some numbers are out of range!")
                except:
                    print("❌ Invalid selection format!")
        
        print(f"\n✅ Selected {len(recipients)} recipients:")
        for recipient in recipients:
            print(f"  • {recipient['name']} ({recipient['type']})")
        
        return recipients
    
    def compose_message(self):
        """Compose message to send"""
        print("\n✍️  COMPOSE YOUR MESSAGE:")
        print("(Press Enter twice to finish, or type 'cancel' to abort)")
        
        lines = []
        empty_lines = 0
        
        while True:
            line = input()
            if line.lower() == 'cancel':
                return None
            
            if line == '':
                empty_lines += 1
                if empty_lines >= 2:
                    break
            else:
                empty_lines = 0
            
            lines.append(line)
        
        # Remove trailing empty lines
        while lines and lines[-1] == '':
            lines.pop()
        
        message = '\n'.join(lines)
        
        if not message.strip():
            print("❌ Message cannot be empty!")
            return None
        
        print(f"\n📝 Your message ({len(message)} characters):")
        print("-" * 40)
        print(message)
        print("-" * 40)
        
        confirm = input("\nSend this message? (y/n): ").strip().lower()
        return message if confirm == 'y' else None
    
    async def send_messages(self, recipients, message, delay=1):
        """Send messages to selected recipients"""
        print(f"\n🚀 Sending message to {len(recipients)} recipients...")

        successful = 0
        failed = 0
        errors = []

        for i, recipient in enumerate(recipients, 1):
            try:
                print(f"[{i}/{len(recipients)}] Sending to {recipient['name']}...", end=' ')

                await self.client.send_message(recipient['entity'], message)
                print("✅")
                successful += 1

                # Rate limiting - wait between messages
                if i < len(recipients):
                    await asyncio.sleep(delay)

            except FloodWaitError as e:
                print(f"⏳ Rate limited, waiting {e.seconds} seconds...")
                await asyncio.sleep(e.seconds)
                # Retry
                try:
                    await self.client.send_message(recipient['entity'], message)
                    print("✅ (retry successful)")
                    successful += 1
                except Exception as retry_error:
                    print("❌ (retry failed)")
                    failed += 1
                    errors.append(f"{recipient['name']}: {str(retry_error)}")

            except (PeerFloodError, UserPrivacyRestrictedError) as e:
                print("❌ (privacy/flood error)")
                failed += 1
                errors.append(f"{recipient['name']}: {str(e)}")

            except Exception as e:
                print(f"❌ ({str(e)})")
                failed += 1
                errors.append(f"{recipient['name']}: {str(e)}")

        print(f"\n📊 RESULTS:")
        print(f"✅ Successful: {successful}")
        print(f"❌ Failed: {failed}")
        if successful + failed > 0:
            print(f"📈 Success rate: {successful/(successful+failed)*100:.1f}%")

        # Show first few errors if any
        if errors and len(errors) <= 5:
            print(f"\n❌ Errors:")
            for error in errors:
                print(f"  • {error}")
        elif errors:
            print(f"\n❌ First 5 errors (total: {len(errors)}):")
            for error in errors[:5]:
                print(f"  • {error}")

    async def send_to_all_groups(self):
        """Send message to all groups with bulk options"""
        contacts = await self.get_contacts()
        if not contacts:
            print("❌ No contacts found!")
            return

        groups = [c for c in contacts if c['type'] in ['Group', 'Supergroup']]
        if not groups:
            print("❌ No groups found!")
            return

        print(f"\n👨‍👩‍👧‍👦 Found {len(groups)} groups")

        # Show groups with pagination
        self.display_contacts(contacts, show_all=False)

        print(f"\n📤 BULK GROUP MESSAGING OPTIONS:")
        print("1. 🎯 Send to ALL groups")
        print("2. 📝 Select specific groups")
        print("3. 📊 Send to groups by range")
        print("4. 🔍 Search and select groups")
        print("5. 🔙 Back to main menu")

        choice = input("\nSelect option (1-5): ").strip()

        if choice == '1':
            # Send to all groups
            print(f"\n⚠️  You are about to send a message to ALL {len(groups)} groups!")
            confirm = input("Are you sure? Type 'YES' to confirm: ").strip()
            if confirm != 'YES':
                print("❌ Operation cancelled!")
                return

            message = self.compose_message()
            if not message:
                return

            # Ask for delay between messages
            print(f"\n⏱️  RATE LIMITING:")
            print("1. Fast (0.5s delay) - Risk of rate limiting")
            print("2. Normal (1s delay) - Recommended")
            print("3. Safe (2s delay) - Very safe")
            print("4. Custom delay")

            delay_choice = input("Select delay option (1-4): ").strip()
            delay = 1  # default

            if delay_choice == '1':
                delay = 0.5
            elif delay_choice == '2':
                delay = 1
            elif delay_choice == '3':
                delay = 2
            elif delay_choice == '4':
                try:
                    delay = float(input("Enter delay in seconds: "))
                except:
                    delay = 1

            await self.send_messages(groups, message, delay)

        elif choice == '2':
            # Select specific groups
            recipients = self.select_recipients(groups)
            if not recipients:
                return

            message = self.compose_message()
            if not message:
                return

            await self.send_messages(recipients, message)

        elif choice == '3':
            # Send by range
            print(f"\nEnter range (1-{len(groups)}):")
            try:
                start = int(input("Start index: "))
                end = int(input("End index: "))

                if 1 <= start <= len(groups) and 1 <= end <= len(groups) and start <= end:
                    selected_groups = groups[start-1:end]
                    print(f"\n✅ Selected {len(selected_groups)} groups (#{start} to #{end})")

                    message = self.compose_message()
                    if not message:
                        return

                    await self.send_messages(selected_groups, message)
                else:
                    print("❌ Invalid range!")
            except ValueError:
                print("❌ Invalid input!")

        elif choice == '4':
            # Search groups
            search_term = input("\nEnter search term: ").strip().lower()
            if not search_term:
                print("❌ Search term cannot be empty!")
                return

            matching_groups = [g for g in groups if search_term in g['name'].lower()]

            if not matching_groups:
                print(f"❌ No groups found matching '{search_term}'")
                return

            print(f"\n🔍 Found {len(matching_groups)} groups matching '{search_term}':")
            for i, group in enumerate(matching_groups, 1):
                print(f"  {i:2d}. {group['name']} ({group['type']})")

            print(f"\nSend to all {len(matching_groups)} matching groups?")
            confirm = input("Type 'yes' to confirm: ").strip().lower()

            if confirm == 'yes':
                message = self.compose_message()
                if not message:
                    return

                await self.send_messages(matching_groups, message)
            else:
                print("❌ Operation cancelled!")

        elif choice == '5':
            return
        else:
            print("❌ Invalid option!")
    
    async def main_menu(self):
        """Main application menu"""
        while True:
            print("\n" + "="*60)
            print("🚀 TELEGRAM DM BOT - BULK MESSAGING")
            print("="*60)
            print("1. 📤 Send messages to selected contacts")
            print("2. 👨‍👩‍👧‍👦 Bulk send to groups (FAST)")
            print("3. 📊 View contact statistics")
            print("4. 🔄 Refresh contacts")
            print("5. 🚪 Exit")
            print("="*60)

            choice = input("Select option (1-5): ").strip()

            if choice == '1':
                contacts = await self.get_contacts()
                if not contacts:
                    print("❌ No contacts found!")
                    continue

                self.display_contacts(contacts)
                recipients = self.select_recipients(contacts)

                if not recipients:
                    print("❌ No recipients selected!")
                    continue

                message = self.compose_message()
                if not message:
                    print("❌ Message composition cancelled!")
                    continue

                await self.send_messages(recipients, message)

            elif choice == '2':
                await self.send_to_all_groups()

            elif choice == '3':
                contacts = await self.get_contacts()
                if contacts:
                    self.show_statistics(contacts)

            elif choice == '4':
                await self.get_contacts()

            elif choice == '5':
                print("👋 Goodbye!")
                break

            else:
                print("❌ Invalid option!")

    def show_statistics(self, contacts):
        """Show contact statistics"""
        users = [c for c in contacts if c['type'] == 'User']
        groups = [c for c in contacts if c['type'] in ['Group', 'Supergroup']]
        channels = [c for c in contacts if c['type'] == 'Channel']
        bots = [c for c in contacts if c['type'] == 'Bot']

        print("\n" + "="*50)
        print("📊 CONTACT STATISTICS")
        print("="*50)
        print(f"👥 Users: {len(users)}")
        print(f"👨‍👩‍👧‍👦 Groups: {len(groups)}")
        print(f"📢 Channels: {len(channels)}")
        print(f"🤖 Bots: {len(bots)}")
        print(f"📱 Total: {len(contacts)}")
        print("="*50)

        if groups:
            print(f"\n🎯 BULK MESSAGING POTENTIAL:")
            print(f"   • You can send to {len(groups)} groups at once")
            print(f"   • Estimated time (1s delay): {len(groups)} seconds")
            print(f"   • Estimated time (2s delay): {len(groups)*2} seconds")
    
    def show_welcome(self):
        """Show welcome screen and setup information"""
        print("\n" + "="*70)
        print("🚀 TELEGRAM DM BOT - BULK MESSAGING TOOL")
        print("="*70)
        print("📱 Send messages to 1000+ groups efficiently!")
        print("🎯 Perfect for bulk messaging, announcements, and marketing")
        print("⚡ Fast, safe, and user-friendly")
        print("="*70)

        print("\n🔧 FIRST TIME SETUP:")
        print("1. We'll help you create a Telegram API application")
        print("2. Get your API credentials (takes 2 minutes)")
        print("3. Login with your Telegram account")
        print("4. Start bulk messaging!")

        print("\n📋 WHAT YOU'LL NEED:")
        print("• Your Telegram account (phone number)")
        print("• Access to your phone for verification codes")
        print("• 2 minutes to create API application")

        print("\n" + "="*70)
        input("Press Enter to continue with setup...")

    async def run(self):
        """Run the bot"""
        self.show_welcome()

        if await self.connect():
            try:
                await self.main_menu()
            finally:
                if self.client:
                    await self.client.disconnect()
                    print("📱 Disconnected from Telegram")
        else:
            print("❌ Failed to start bot")

if __name__ == "__main__":
    bot = TelegramDMBot()
    asyncio.run(bot.run())
