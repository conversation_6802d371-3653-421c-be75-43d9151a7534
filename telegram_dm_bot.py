#!/usr/bin/env python3
"""
🚀 PUMPX TELEGRAM SHILL BOT - Professional Marketing Dashboard
Ultra-modern GUI with ALL features working perfectly - Real functionality only
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import asyncio
import threading
import time
from datetime import datetime
import os
import sys
import re

# Telegram imports
from telethon import TelegramClient
from telethon.errors import FloodWaitError, PeerFloodError, UserPrivacyRestrictedError
from telethon.errors import SessionPasswordNeededError, InviteHashExpiredError, InviteHashInvalidError, UserAlreadyParticipantError
from telethon.tl.functions.channels import JoinChannelRequest
from telethon.tl.functions.messages import ImportChatInviteRequest, CheckChatInviteRequest
from telethon.tl.types import ChatInviteAlready, ChatInvite

class PumpXShillBot:
    def __init__(self):
        self.root = tk.Tk()
        
        # Telegram credentials
        self.api_id = 23806500
        self.api_hash = "c9330b83e3e23125e890eaa41902b193"
        self.session_name = "telegram_dm_session"
        
        # Initialize all variables
        self.client = None
        self.is_connected = False
        self.groups = []
        self.selected_groups = []
        self.event_loop = None
        self.event_loop_thread = None
        self.automation_running = False
        self.discovery_running = False
        self.selected_photo_path = None
        self.discovered_groups = []
        self.campaign_stats = {
            'messages_sent': 0,
            'success_count': 0,
            'failed_count': 0,
            'groups_reached': 0
        }
        
        # Login state variables
        self.login_step = "disconnected"  # disconnected, phone, code, password, connected
        self.phone_number = ""
        self.verification_code = ""
        self.password = ""
        
        # Message variables
        self.msg_type_var = None
        self.message_text = None
        self.groups_listbox = None
        
        # Setup the modern GUI
        self.setup_modern_gui()
        
        # Start event loop
        self.start_event_loop()
    
    def setup_modern_gui(self):
        """Setup the ultra-modern dashboard GUI"""
        self.root.title("🚀 PUMPX TELEGRAM SHILL BOT - Professional Marketing Dashboard")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#0a0a0a')
        self.root.state('zoomed')
        
        # Modern color scheme
        self.colors = {
            'bg_primary': '#0a0a0a',      # Ultra dark background
            'bg_secondary': '#1a1a1a',    # Card backgrounds
            'bg_accent': '#2a2a2a',       # Hover/active states
            'text_primary': '#ffffff',    # Main text
            'text_secondary': '#b0b0b0',  # Secondary text
            'accent_blue': '#00d4ff',     # Neon blue
            'accent_green': '#00ff88',    # Neon green
            'accent_red': '#ff4757',      # Neon red
            'accent_orange': '#ffa502',   # Neon orange
            'accent_purple': '#a55eea',   # Neon purple
            'border': '#333333'           # Borders
        }
        
        # Create the dashboard
        self.create_dashboard()
    
    def create_dashboard(self):
        """Create the main dashboard layout"""
        # Top header bar
        self.create_header()
        
        # Main content area
        self.create_main_content()
        
        # Bottom status bar
        self.create_status_bar()
    
    def create_header(self):
        """Create the top header with title and main controls"""
        header = tk.Frame(self.root, bg=self.colors['bg_secondary'], height=80)
        header.pack(fill=tk.X)
        header.pack_propagate(False)
        
        # Left side - Title and status
        left_header = tk.Frame(header, bg=self.colors['bg_secondary'])
        left_header.pack(side=tk.LEFT, fill=tk.Y, padx=20, pady=15)
        
        title_label = tk.Label(left_header, 
                              text="🚀 PUMPX SHILL BOT", 
                              font=("Segoe UI", 24, "bold"),
                              bg=self.colors['bg_secondary'],
                              fg=self.colors['accent_blue'])
        title_label.pack(anchor=tk.W)
        
        self.status_label = tk.Label(left_header,
                                    text="🔴 Disconnected",
                                    font=("Segoe UI", 12),
                                    bg=self.colors['bg_secondary'],
                                    fg=self.colors['accent_red'])
        self.status_label.pack(anchor=tk.W)
        
        # Right side - Main action buttons
        right_header = tk.Frame(header, bg=self.colors['bg_secondary'])
        right_header.pack(side=tk.RIGHT, fill=tk.Y, padx=20, pady=15)
        
        # Connection button
        self.connect_btn = tk.Button(right_header,
                                    text="🔗 CONNECT TO TELEGRAM",
                                    command=self.connect_to_telegram,
                                    bg=self.colors['accent_blue'],
                                    fg='white',
                                    font=("Segoe UI", 12, "bold"),
                                    relief='flat',
                                    padx=20, pady=10,
                                    cursor='hand2')
        self.connect_btn.pack(side=tk.RIGHT, padx=5)
        
        # Emergency stop button
        self.emergency_stop_btn = tk.Button(right_header,
                                           text="🛑 EMERGENCY STOP",
                                           command=self.emergency_stop,
                                           bg=self.colors['accent_red'],
                                           fg='white',
                                           font=("Segoe UI", 12, "bold"),
                                           relief='flat',
                                           padx=20, pady=10,
                                           cursor='hand2')
        self.emergency_stop_btn.pack(side=tk.RIGHT, padx=5)
    
    def create_main_content(self):
        """Create the main content area with all features"""
        main_container = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Left column - Login & Message Composer
        self.left_column = tk.Frame(main_container, bg=self.colors['bg_primary'], width=500)
        self.left_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=5)
        self.left_column.pack_propagate(False)
        
        # Middle column - Live Monitor & Groups
        self.middle_column = tk.Frame(main_container, bg=self.colors['bg_primary'])
        self.middle_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # Right column - Automation & Discovery
        self.right_column = tk.Frame(main_container, bg=self.colors['bg_primary'], width=450)
        self.right_column.pack(side=tk.RIGHT, fill=tk.BOTH, expand=False, padx=5)
        self.right_column.pack_propagate(False)
        
        # Setup each column
        self.setup_left_column()
        self.setup_middle_column()
        self.setup_right_column()
    
    def create_status_bar(self):
        """Create the bottom status bar"""
        status_bar = tk.Frame(self.root, bg=self.colors['bg_secondary'], height=35)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        status_bar.pack_propagate(False)
        
        # Status info
        self.stats_label = tk.Label(status_bar,
                                   text="Ready • 0 groups loaded • 0 messages sent • 0% success rate",
                                   font=("Segoe UI", 11),
                                   bg=self.colors['bg_secondary'],
                                   fg=self.colors['text_secondary'])
        self.stats_label.pack(side=tk.LEFT, padx=20, pady=8)
        
        # Version info
        version_label = tk.Label(status_bar,
                                text="PumpX v3.0 Professional Shill Bot",
                                font=("Segoe UI", 11, "bold"),
                                bg=self.colors['bg_secondary'],
                                fg=self.colors['accent_purple'])
        version_label.pack(side=tk.RIGHT, padx=20, pady=8)
    
    def setup_left_column(self):
        """Setup left column - Login & Message Composer"""
        # Login Card
        self.create_login_card()

        # Message Composer Card
        self.create_message_composer_card()

    def create_card(self, parent, title):
        """Create a modern card widget"""
        card_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], relief='flat', bd=1)
        card_frame.pack(fill=tk.X, pady=8, padx=5)

        # Card header
        header = tk.Frame(card_frame, bg=self.colors['bg_accent'], height=40)
        header.pack(fill=tk.X)
        header.pack_propagate(False)

        title_label = tk.Label(header, text=title,
                              font=("Segoe UI", 14, "bold"),
                              bg=self.colors['bg_accent'],
                              fg=self.colors['text_primary'])
        title_label.pack(side=tk.LEFT, padx=15, pady=10)

        # Card content
        content = tk.Frame(card_frame, bg=self.colors['bg_secondary'])
        content.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        return content

    def create_login_card(self):
        """Create the login card with phone, OTP, and password"""
        login_content = self.create_card(self.left_column, "🔐 Telegram Login")

        # Phone number input
        phone_frame = tk.Frame(login_content, bg=self.colors['bg_secondary'])
        phone_frame.pack(fill=tk.X, pady=5)

        tk.Label(phone_frame, text="📱 Phone Number:",
                font=("Segoe UI", 11, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W)

        phone_input_frame = tk.Frame(phone_frame, bg=self.colors['bg_secondary'])
        phone_input_frame.pack(fill=tk.X, pady=5)

        self.phone_entry = tk.Entry(phone_input_frame,
                                   font=("Segoe UI", 12),
                                   bg=self.colors['bg_accent'],
                                   fg=self.colors['text_primary'],
                                   insertbackground=self.colors['text_primary'],
                                   relief='flat',
                                   bd=5)
        self.phone_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0,10))
        self.phone_entry.insert(0, "+1")

        self.send_code_btn = tk.Button(phone_input_frame,
                                      text="📨 Send Code",
                                      command=self.send_verification_code,
                                      bg=self.colors['accent_blue'],
                                      fg='white',
                                      font=("Segoe UI", 10, "bold"),
                                      relief='flat',
                                      padx=15, pady=8,
                                      cursor='hand2')
        self.send_code_btn.pack(side=tk.RIGHT)

        # OTP code input
        code_frame = tk.Frame(login_content, bg=self.colors['bg_secondary'])
        code_frame.pack(fill=tk.X, pady=5)

        tk.Label(code_frame, text="📨 Verification Code:",
                font=("Segoe UI", 11, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W)

        code_input_frame = tk.Frame(code_frame, bg=self.colors['bg_secondary'])
        code_input_frame.pack(fill=tk.X, pady=5)

        self.code_entry = tk.Entry(code_input_frame,
                                  font=("Segoe UI", 12),
                                  bg=self.colors['bg_accent'],
                                  fg=self.colors['text_primary'],
                                  insertbackground=self.colors['text_primary'],
                                  relief='flat',
                                  bd=5)
        self.code_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0,10))

        self.verify_code_btn = tk.Button(code_input_frame,
                                        text="✅ Verify",
                                        command=self.verify_code,
                                        bg=self.colors['accent_green'],
                                        fg='white',
                                        font=("Segoe UI", 10, "bold"),
                                        relief='flat',
                                        padx=15, pady=8,
                                        cursor='hand2')
        self.verify_code_btn.pack(side=tk.RIGHT)

        # Password input (for 2FA)
        password_frame = tk.Frame(login_content, bg=self.colors['bg_secondary'])
        password_frame.pack(fill=tk.X, pady=5)

        tk.Label(password_frame, text="🔒 2FA Password (if enabled):",
                font=("Segoe UI", 11, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W)

        password_input_frame = tk.Frame(password_frame, bg=self.colors['bg_secondary'])
        password_input_frame.pack(fill=tk.X, pady=5)

        self.password_entry = tk.Entry(password_input_frame,
                                      font=("Segoe UI", 12),
                                      bg=self.colors['bg_accent'],
                                      fg=self.colors['text_primary'],
                                      insertbackground=self.colors['text_primary'],
                                      relief='flat',
                                      bd=5,
                                      show='*')
        self.password_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0,10))

        self.login_btn = tk.Button(password_input_frame,
                                  text="🚀 Login",
                                  command=self.complete_login,
                                  bg=self.colors['accent_purple'],
                                  fg='white',
                                  font=("Segoe UI", 10, "bold"),
                                  relief='flat',
                                  padx=15, pady=8,
                                  cursor='hand2')
        self.login_btn.pack(side=tk.RIGHT)

    def create_message_composer_card(self):
        """Create the message composer card"""
        composer_content = self.create_card(self.left_column, "📝 Message Composer")

        # Message composition options
        options_frame = tk.Frame(composer_content, bg=self.colors['bg_secondary'])
        options_frame.pack(fill=tk.X, pady=5)

        tk.Label(options_frame, text="Message Components:",
                font=("Segoe UI", 11, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W)

        # Checkboxes for different components
        components_frame = tk.Frame(options_frame, bg=self.colors['bg_secondary'])
        components_frame.pack(fill=tk.X, pady=5)

        self.include_text = tk.BooleanVar(value=True)
        self.include_photo = tk.BooleanVar(value=False)
        self.include_links = tk.BooleanVar(value=False)

        tk.Checkbutton(components_frame, text="📄 Text Message",
                      variable=self.include_text,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_blue'],
                      font=("Segoe UI", 10)).pack(side=tk.LEFT, padx=10)

        tk.Checkbutton(components_frame, text="🖼️ Photo",
                      variable=self.include_photo, command=self.on_photo_toggle,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_blue'],
                      font=("Segoe UI", 10)).pack(side=tk.LEFT, padx=10)

        tk.Checkbutton(components_frame, text="🔗 Links",
                      variable=self.include_links, command=self.on_links_toggle,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_blue'],
                      font=("Segoe UI", 10)).pack(side=tk.LEFT, padx=10)

        # Photo selection frame
        self.photo_frame = tk.Frame(composer_content, bg=self.colors['bg_secondary'])

        photo_btn_frame = tk.Frame(self.photo_frame, bg=self.colors['bg_secondary'])
        photo_btn_frame.pack(fill=tk.X, pady=5)

        self.photo_btn = tk.Button(photo_btn_frame, text="📁 Select Photo",
                                  command=self.select_photo,
                                  bg=self.colors['accent_orange'], fg='white',
                                  font=("Segoe UI", 10, "bold"), relief='flat',
                                  padx=15, pady=5, cursor='hand2')
        self.photo_btn.pack(side=tk.LEFT)

        self.photo_label = tk.Label(photo_btn_frame, text="No photo selected",
                                   bg=self.colors['bg_secondary'], fg=self.colors['text_secondary'],
                                   font=("Segoe UI", 9))
        self.photo_label.pack(side=tk.LEFT, padx=10)

        # Links input frame (initially hidden)
        self.links_frame = tk.Frame(composer_content, bg=self.colors['bg_secondary'])

        tk.Label(self.links_frame, text="🔗 Links (one per line):",
                font=("Segoe UI", 11, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W, pady=(5,0))

        self.links_text = scrolledtext.ScrolledText(self.links_frame, height=3,
                                                   font=("Segoe UI", 10),
                                                   bg=self.colors['bg_accent'],
                                                   fg=self.colors['text_primary'],
                                                   insertbackground=self.colors['text_primary'],
                                                   relief='flat')
        self.links_text.pack(fill=tk.X, pady=5)
        self.links_text.insert(1.0, "https://example.com\nhttps://telegram.me/your_channel")

        # Message text area
        tk.Label(composer_content, text="Message Content:",
                font=("Segoe UI", 11, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W, pady=(10,5))

        self.message_text = scrolledtext.ScrolledText(composer_content, height=8,
                                                     font=("Segoe UI", 11),
                                                     bg=self.colors['bg_accent'],
                                                     fg=self.colors['text_primary'],
                                                     insertbackground=self.colors['text_primary'],
                                                     relief='flat')
        self.message_text.pack(fill=tk.X, pady=5)
        self.message_text.bind('<KeyRelease>', self.update_char_count)

        # Character counter
        self.char_count_label = tk.Label(composer_content, text="0/4096 characters",
                                        bg=self.colors['bg_secondary'],
                                        fg=self.colors['text_secondary'],
                                        font=("Segoe UI", 9))
        self.char_count_label.pack(anchor=tk.E, pady=2)

        # Quick templates
        templates_frame = tk.Frame(composer_content, bg=self.colors['bg_secondary'])
        templates_frame.pack(fill=tk.X, pady=10)

        tk.Label(templates_frame, text="Quick Templates:",
                font=("Segoe UI", 11, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W)

        template_btns_frame = tk.Frame(templates_frame, bg=self.colors['bg_secondary'])
        template_btns_frame.pack(fill=tk.X, pady=5)

        templates = [
            ("🚀 Crypto", "🚀 New Token Alert! 🚀\n\n💎 Token: [TOKEN_NAME]\n📈 Price: $[PRICE]\n🔗 Website: [WEBSITE]\n\n#crypto #pump #gem"),
            ("💰 Trading", "💰 Trading Signal! 💰\n\n📊 Pair: [PAIR]\n📈 Entry: [ENTRY]\n🎯 Target: [TARGET]\n🛑 Stop: [STOP]\n\n#trading #signal"),
            ("🎯 Shill", "🎯 MASSIVE PUMP INCOMING! 🎯\n\n🔥 [PROJECT_NAME]\n💸 Easy 100x potential!\n⏰ Don't miss out!\n🔗 [LINK]\n\n#shill #pump #moon")
        ]

        for name, template in templates:
            btn = tk.Button(template_btns_frame, text=name,
                           command=lambda t=template: self.insert_template(t),
                           bg=self.colors['accent_purple'], fg='white',
                           font=("Segoe UI", 9, "bold"), relief='flat',
                           padx=10, pady=3, cursor='hand2')
            btn.pack(side=tk.LEFT, padx=3)

        # Automation options
        automation_options_frame = tk.Frame(composer_content, bg=self.colors['bg_secondary'])
        automation_options_frame.pack(fill=tk.X, pady=10)

        tk.Label(automation_options_frame, text="🔄 Automation Options:",
                font=("Segoe UI", 11, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W)

        auto_options_frame = tk.Frame(automation_options_frame, bg=self.colors['bg_secondary'])
        auto_options_frame.pack(fill=tk.X, pady=5)

        self.enable_automation = tk.BooleanVar(value=False)
        tk.Checkbutton(auto_options_frame, text="🔁 Enable 24/7 Automation",
                      variable=self.enable_automation, command=self.on_automation_toggle,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_green'],
                      font=("Segoe UI", 10)).pack(anchor=tk.W, pady=2)

        # Automation settings (initially hidden)
        self.automation_settings_frame = tk.Frame(automation_options_frame, bg=self.colors['bg_secondary'])

        settings_grid = tk.Frame(self.automation_settings_frame, bg=self.colors['bg_secondary'])
        settings_grid.pack(fill=tk.X, pady=5)

        # Interval setting
        tk.Label(settings_grid, text="⏱️ Send Interval:",
                font=("Segoe UI", 10),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).grid(row=0, column=0, sticky='w', padx=5)

        self.interval_var = tk.StringVar(value="30")
        interval_entry = tk.Entry(settings_grid, textvariable=self.interval_var,
                                 font=("Segoe UI", 10), width=5,
                                 bg=self.colors['bg_accent'], fg=self.colors['text_primary'],
                                 insertbackground=self.colors['text_primary'], relief='flat')
        interval_entry.grid(row=0, column=1, padx=5)

        tk.Label(settings_grid, text="minutes",
                font=("Segoe UI", 10),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_secondary']).grid(row=0, column=2, sticky='w')

        # Repeat count
        tk.Label(settings_grid, text="🔄 Repeat Count:",
                font=("Segoe UI", 10),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).grid(row=1, column=0, sticky='w', padx=5, pady=5)

        self.repeat_var = tk.StringVar(value="∞")
        repeat_entry = tk.Entry(settings_grid, textvariable=self.repeat_var,
                               font=("Segoe UI", 10), width=5,
                               bg=self.colors['bg_accent'], fg=self.colors['text_primary'],
                               insertbackground=self.colors['text_primary'], relief='flat')
        repeat_entry.grid(row=1, column=1, padx=5, pady=5)

        tk.Label(settings_grid, text="(∞ = unlimited)",
                font=("Segoe UI", 10),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_secondary']).grid(row=1, column=2, sticky='w', pady=5)

        # Send buttons
        send_frame = tk.Frame(composer_content, bg=self.colors['bg_secondary'])
        send_frame.pack(fill=tk.X, pady=15)

        # Single send button (green)
        self.send_once_btn = tk.Button(send_frame, text="🚀 SEND ONCE",
                                      command=self.send_bulk_messages,
                                      bg=self.colors['accent_green'], fg='white',
                                      font=("Segoe UI", 12, "bold"),
                                      relief='flat', padx=20, pady=12,
                                      cursor='hand2')
        self.send_once_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)

        # Automated send button (blue)
        self.send_auto_btn = tk.Button(send_frame, text="🔄 START AUTOMATION",
                                      command=self.start_automated_sending,
                                      bg=self.colors['accent_blue'], fg='white',
                                      font=("Segoe UI", 12, "bold"),
                                      relief='flat', padx=20, pady=12,
                                      cursor='hand2')
        self.send_auto_btn.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=2)

    def create_monitor_card(self):
        """Create the live monitor card"""
        monitor_content = self.create_card(self.middle_column, "📺 Live Activity Monitor")

        # Monitor controls
        controls_frame = tk.Frame(monitor_content, bg=self.colors['bg_secondary'])
        controls_frame.pack(fill=tk.X, pady=5)

        tk.Button(controls_frame, text="▶️ Start",
                 command=self.start_monitor,
                 bg=self.colors['accent_green'], fg='white',
                 font=("Segoe UI", 9, "bold"), relief='flat',
                 padx=10, pady=3, cursor='hand2').pack(side=tk.LEFT, padx=2)

        tk.Button(controls_frame, text="⏸️ Pause",
                 command=self.pause_monitor,
                 bg=self.colors['accent_orange'], fg='white',
                 font=("Segoe UI", 9, "bold"), relief='flat',
                 padx=10, pady=3, cursor='hand2').pack(side=tk.LEFT, padx=2)

        tk.Button(controls_frame, text="🗑️ Clear",
                 command=self.clear_monitor,
                 bg=self.colors['accent_red'], fg='white',
                 font=("Segoe UI", 9, "bold"), relief='flat',
                 padx=10, pady=3, cursor='hand2').pack(side=tk.LEFT, padx=2)

        self.auto_scroll = tk.BooleanVar(value=True)
        tk.Checkbutton(controls_frame, text="📜 Auto-scroll",
                      variable=self.auto_scroll,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_blue'],
                      font=("Segoe UI", 9)).pack(side=tk.RIGHT)

        # Live monitor display
        self.monitor_text = scrolledtext.ScrolledText(monitor_content, height=15,
                                                     font=("Consolas", 10),
                                                     bg='#000000', fg=self.colors['accent_green'],
                                                     insertbackground=self.colors['accent_green'],
                                                     relief='flat')
        self.monitor_text.pack(fill=tk.BOTH, expand=True, pady=5)

        # Add welcome message
        self.monitor_text.insert(tk.END, "🚀 PumpX Shill Bot Monitor Ready\n")
        self.monitor_text.insert(tk.END, "=" * 50 + "\n")

    def create_groups_card(self):
        """Create the groups manager card"""
        groups_content = self.create_card(self.middle_column, "👥 Groups Manager")

        # Group controls
        group_controls = tk.Frame(groups_content, bg=self.colors['bg_secondary'])
        group_controls.pack(fill=tk.X, pady=5)

        tk.Button(group_controls, text="🔄 Refresh",
                 command=self.refresh_groups,
                 bg=self.colors['accent_blue'], fg='white',
                 font=("Segoe UI", 9, "bold"), relief='flat',
                 padx=10, pady=5, cursor='hand2').pack(side=tk.LEFT, padx=2)

        tk.Button(group_controls, text="✅ Select All",
                 command=self.select_all_groups,
                 bg=self.colors['accent_blue'], fg='white',
                 font=("Segoe UI", 9, "bold"), relief='flat',
                 padx=10, pady=5, cursor='hand2').pack(side=tk.LEFT, padx=2)

        tk.Button(group_controls, text="❌ Clear",
                 command=self.clear_selection,
                 bg=self.colors['accent_red'], fg='white',
                 font=("Segoe UI", 9, "bold"), relief='flat',
                 padx=10, pady=5, cursor='hand2').pack(side=tk.LEFT, padx=2)

        # Groups list
        self.groups_listbox = tk.Listbox(groups_content, height=12,
                                        selectmode=tk.MULTIPLE,
                                        bg=self.colors['bg_accent'],
                                        fg=self.colors['text_primary'],
                                        font=("Segoe UI", 10),
                                        selectbackground=self.colors['accent_blue'],
                                        relief='flat')
        self.groups_listbox.pack(fill=tk.BOTH, expand=True, pady=5)

        # Group stats
        self.group_stats_label = tk.Label(groups_content, text="0 groups loaded • 0 selected",
                                         bg=self.colors['bg_secondary'],
                                         fg=self.colors['text_secondary'],
                                         font=("Segoe UI", 10))
        self.group_stats_label.pack(anchor=tk.W, pady=2)

    def create_automation_card(self):
        """Create the automation control card"""
        automation_content = self.create_card(self.right_column, "🔄 Automation Control")

        # Status display
        self.automation_status = tk.Label(automation_content,
                                         text="🔴 STOPPED",
                                         font=("Segoe UI", 18, "bold"),
                                         bg=self.colors['bg_secondary'],
                                         fg=self.colors['accent_red'])
        self.automation_status.pack(pady=10)

        # Control buttons
        control_frame = tk.Frame(automation_content, bg=self.colors['bg_secondary'])
        control_frame.pack(fill=tk.X, pady=10)

        self.start_automation_btn = tk.Button(control_frame,
                                             text="🟢 START",
                                             command=self.start_automation,
                                             bg=self.colors['accent_green'], fg='white',
                                             font=("Segoe UI", 12, "bold"),
                                             relief='flat', padx=20, pady=10,
                                             cursor='hand2')
        self.start_automation_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)

        self.stop_automation_btn = tk.Button(control_frame,
                                            text="🔴 STOP",
                                            command=self.stop_automation,
                                            bg=self.colors['accent_red'], fg='white',
                                            font=("Segoe UI", 12, "bold"),
                                            relief='flat', padx=20, pady=10,
                                            cursor='hand2')
        self.stop_automation_btn.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=2)

        # Automation settings
        settings_frame = tk.Frame(automation_content, bg=self.colors['bg_secondary'])
        settings_frame.pack(fill=tk.X, pady=10)

        self.continuous_mode = tk.BooleanVar(value=False)
        tk.Checkbutton(settings_frame, text="🔁 Run continuously (24/7)",
                      variable=self.continuous_mode,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_blue'],
                      font=("Segoe UI", 10)).pack(anchor=tk.W, pady=2)

        self.auto_comment = tk.BooleanVar(value=False)
        tk.Checkbutton(settings_frame, text="💬 Auto-comment on pinned messages",
                      variable=self.auto_comment,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_blue'],
                      font=("Segoe UI", 10)).pack(anchor=tk.W, pady=2)

        # Campaign stats
        stats_frame = tk.Frame(automation_content, bg=self.colors['bg_secondary'])
        stats_frame.pack(fill=tk.X, pady=10)

        tk.Label(stats_frame, text="📊 Campaign Statistics:",
                font=("Segoe UI", 11, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W)

        self.campaign_stats_label = tk.Label(stats_frame,
                                            text="Messages: 0 • Success: 0% • Groups: 0",
                                            bg=self.colors['bg_secondary'],
                                            fg=self.colors['text_secondary'],
                                            font=("Segoe UI", 10))
        self.campaign_stats_label.pack(anchor=tk.W, pady=2)

    def create_discovery_card(self):
        """Create the group discovery card"""
        discovery_content = self.create_card(self.right_column, "🔍 Group Discovery")

        # Discovery controls
        discovery_controls = tk.Frame(discovery_content, bg=self.colors['bg_secondary'])
        discovery_controls.pack(fill=tk.X, pady=5)

        self.start_discovery_btn = tk.Button(discovery_controls,
                                            text="🔍 START DISCOVERY",
                                            command=self.start_discovery,
                                            bg=self.colors['accent_blue'], fg='white',
                                            font=("Segoe UI", 11, "bold"),
                                            relief='flat', padx=15, pady=8,
                                            cursor='hand2')
        self.start_discovery_btn.pack(fill=tk.X, pady=2)

        self.stop_discovery_btn = tk.Button(discovery_controls,
                                           text="⏹️ STOP DISCOVERY",
                                           command=self.stop_discovery,
                                           bg=self.colors['accent_red'], fg='white',
                                           font=("Segoe UI", 11, "bold"),
                                           relief='flat', padx=15, pady=8,
                                           cursor='hand2')
        self.stop_discovery_btn.pack(fill=tk.X, pady=2)

        # Discovery status
        self.discovery_status = tk.Label(discovery_content,
                                        text="🔴 Discovery: STOPPED",
                                        bg=self.colors['bg_secondary'],
                                        fg=self.colors['accent_red'],
                                        font=("Segoe UI", 11, "bold"))
        self.discovery_status.pack(pady=5)

        # Keywords
        tk.Label(discovery_content, text="🔑 Search Keywords:",
                font=("Segoe UI", 11, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W, pady=(10,5))

        self.keywords_text = scrolledtext.ScrolledText(discovery_content, height=3,
                                                      font=("Segoe UI", 10),
                                                      bg=self.colors['bg_accent'],
                                                      fg=self.colors['text_primary'],
                                                      insertbackground=self.colors['text_primary'],
                                                      relief='flat')
        self.keywords_text.pack(fill=tk.X, pady=5)
        self.keywords_text.insert(1.0, "solana, pump, shill, gem, crypto, token, defi, trading, moon, lambo, call, calls, signal, alert")

        # Discovery settings
        disc_settings = tk.Frame(discovery_content, bg=self.colors['bg_secondary'])
        disc_settings.pack(fill=tk.X, pady=10)

        self.auto_join_discovered = tk.BooleanVar(value=True)
        tk.Checkbutton(disc_settings, text="🤝 Auto-join discovered groups",
                      variable=self.auto_join_discovered,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_blue'],
                      font=("Segoe UI", 10)).pack(anchor=tk.W, pady=2)

        self.auto_comment_calls = tk.BooleanVar(value=True)
        tk.Checkbutton(disc_settings, text="💬 Auto-comment on 'calls' posts",
                      variable=self.auto_comment_calls,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_blue'],
                      font=("Segoe UI", 10)).pack(anchor=tk.W, pady=2)

        # Comment template
        tk.Label(discovery_content, text="💬 Comment Template:",
                font=("Segoe UI", 11, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(anchor=tk.W, pady=(10,5))

        self.comment_template_text = scrolledtext.ScrolledText(discovery_content, height=3,
                                                              font=("Segoe UI", 10),
                                                              bg=self.colors['bg_accent'],
                                                              fg=self.colors['text_primary'],
                                                              insertbackground=self.colors['text_primary'],
                                                              relief='flat')
        self.comment_template_text.pack(fill=tk.X, pady=5)
        self.comment_template_text.insert(1.0, "🚀 Great call! Thanks for sharing! 💎\n🔥 Following this one closely! 📈\n💰 LFG! Moon mission activated! 🌙")

        # Discovery stats
        self.disc_stats_frame = tk.Frame(discovery_content, bg=self.colors['bg_secondary'])
        self.disc_stats_frame.pack(fill=tk.X, pady=10)

        self.disc_stats_found = tk.Label(self.disc_stats_frame, text="Groups Found: 0",
                                        bg=self.colors['bg_secondary'],
                                        fg=self.colors['text_secondary'],
                                        font=("Segoe UI", 10))
        self.disc_stats_found.pack(anchor=tk.W)

        self.disc_stats_joined = tk.Label(self.disc_stats_frame, text="Groups Joined: 0",
                                         bg=self.colors['bg_secondary'],
                                         fg=self.colors['text_secondary'],
                                         font=("Segoe UI", 10))
        self.disc_stats_joined.pack(anchor=tk.W)

    # ============================================================================
    # TELEGRAM CONNECTION AND LOGIN METHODS
    # ============================================================================

    def connect_to_telegram(self):
        """Connect to Telegram servers"""
        if self.is_connected:
            messagebox.showinfo("Info", "Already connected to Telegram!")
            return

        self.connect_btn.config(state='disabled', text='Connecting...')
        self.log_message("🔗 Starting connection to Telegram...")

        # Run connection in thread
        threading.Thread(target=self.connect_telegram_thread, daemon=True).start()

    def connect_telegram_thread(self):
        """Connect to Telegram in separate thread"""
        try:
            # Wait for event loop to be ready and running
            max_wait = 100  # 10 seconds
            for i in range(max_wait):
                if self.event_loop is not None and self.event_loop.is_running():
                    break
                time.sleep(0.1)
            else:
                raise Exception("Event loop not ready after 10 seconds")

            self.log_message("🔗 Event loop ready, creating Telegram client...")

            # Run the entire connection process in the event loop
            future = asyncio.run_coroutine_threadsafe(self.connect_and_check_auth(), self.event_loop)
            result = future.result(timeout=60)

            if result:
                self.log_message("✅ Connection and authentication successful!")
            else:
                self.log_message("📱 Please complete login process")

        except Exception as e:
            self.log_message(f"❌ Connection failed: {str(e)}")
            self.log_message(f"❌ Error details: {type(e).__name__}")
            self.root.after(0, lambda: self.connect_btn.config(state='normal', text='🔗 CONNECT TO TELEGRAM'))

    async def connect_and_check_auth(self):
        """Connect to Telegram and check authorization (runs in event loop)"""
        try:
            # Create Telegram client
            self.client = TelegramClient(self.session_name, self.api_id, self.api_hash)

            self.log_message("📡 Connecting to Telegram servers...")

            # Connect to Telegram
            await self.client.connect()

            self.log_message("✅ Connected to Telegram servers!")

            # Check if already logged in
            self.log_message("🔍 Checking authorization status...")
            is_authorized = await self.client.is_user_authorized()

            if is_authorized:
                # Already logged in
                self.log_message("✅ Already authorized, getting user info...")
                me = await self.client.get_me()

                self.log_message(f"✅ Already logged in as: {me.first_name}")
                self.root.after(0, self.on_login_success)
                return True
            else:
                # Need to login
                self.log_message("📱 Please enter your phone number to login")
                self.root.after(0, self.show_login_form)
                return False

        except Exception as e:
            self.log_message(f"❌ Connection error in async function: {str(e)}")
            return False

    def show_login_form(self):
        """Show the login form"""
        self.connect_btn.config(state='disabled', text='Connected ✅')
        self.status_label.config(text="📱 Enter phone number", fg=self.colors['accent_orange'])

    def send_verification_code(self):
        """Send verification code to phone"""
        phone = self.phone_entry.get().strip()
        if not phone:
            messagebox.showerror("Error", "Please enter your phone number")
            return

        if not phone.startswith('+'):
            phone = '+' + phone

        # Check if connected first
        if not self.client:
            messagebox.showerror("Error", "Please connect to Telegram first")
            return

        self.phone_number = phone
        self.send_code_btn.config(state='disabled', text='Sending...')
        self.log_message(f"📨 Sending verification code to {phone}")

        # Send code in thread
        threading.Thread(target=self.send_code_thread, daemon=True).start()

    def send_code_thread(self):
        """Send verification code in separate thread"""
        try:
            # Make sure client is connected and event loop is ready
            if not self.client:
                self.log_message("❌ Not connected to Telegram. Please connect first.")
                self.root.after(0, lambda: self.send_code_btn.config(state='normal', text='📨 Send Code'))
                return

            # Wait for event loop
            max_wait = 50
            for i in range(max_wait):
                if self.event_loop is not None and self.event_loop.is_running():
                    break
                time.sleep(0.1)
            else:
                raise Exception("Event loop not ready")

            # Send code using event loop
            future = asyncio.run_coroutine_threadsafe(
                self.send_code_async(), self.event_loop)
            result = future.result(timeout=30)

            if result:
                self.log_message("✅ Verification code sent!")
                self.root.after(0, lambda: self.send_code_btn.config(state='disabled', text='Code Sent ✅'))
                self.root.after(0, lambda: self.status_label.config(text="📨 Enter verification code", fg=self.colors['accent_orange']))
            else:
                self.log_message("❌ Failed to send verification code")
                self.root.after(0, lambda: self.send_code_btn.config(state='normal', text='📨 Send Code'))

        except Exception as e:
            self.log_message(f"❌ Failed to send code: {str(e)}")
            self.root.after(0, lambda: self.send_code_btn.config(state='normal', text='📨 Send Code'))

    async def send_code_async(self):
        """Send verification code (runs in event loop)"""
        try:
            await self.client.send_code_request(self.phone_number)
            return True
        except Exception as e:
            self.log_message(f"❌ Send code error: {str(e)}")
            return False

    def verify_code(self):
        """Verify the OTP code"""
        code = self.code_entry.get().strip()
        if not code:
            messagebox.showerror("Error", "Please enter the verification code")
            return

        self.verification_code = code
        self.verify_code_btn.config(state='disabled', text='Verifying...')
        self.log_message(f"🔐 Verifying code: {code}")

        # Verify code in thread
        threading.Thread(target=self.verify_code_thread, daemon=True).start()

    def verify_code_thread(self):
        """Verify code in separate thread"""
        try:
            # Make sure client and event loop are ready
            if not self.client:
                self.log_message("❌ Not connected to Telegram. Please connect first.")
                self.root.after(0, lambda: self.verify_code_btn.config(state='normal', text='✅ Verify'))
                return

            # Wait for event loop
            max_wait = 50
            for i in range(max_wait):
                if self.event_loop is not None and self.event_loop.is_running():
                    break
                time.sleep(0.1)
            else:
                raise Exception("Event loop not ready")

            # Verify code using event loop
            future = asyncio.run_coroutine_threadsafe(
                self.verify_code_async(), self.event_loop)
            result = future.result(timeout=30)

            if result == "success":
                self.log_message("✅ Login successful!")
                self.root.after(0, self.on_login_success)
            elif result == "2fa_required":
                self.log_message("🔒 2FA password required")
                self.root.after(0, lambda: self.verify_code_btn.config(state='disabled', text='2FA Required'))
                self.root.after(0, lambda: self.status_label.config(text="🔒 Enter 2FA password", fg=self.colors['accent_orange']))
            else:
                self.log_message("❌ Code verification failed")
                self.root.after(0, lambda: self.verify_code_btn.config(state='normal', text='✅ Verify'))

        except Exception as e:
            self.log_message(f"❌ Code verification failed: {str(e)}")
            self.root.after(0, lambda: self.verify_code_btn.config(state='normal', text='✅ Verify'))

    async def verify_code_async(self):
        """Verify code (runs in event loop)"""
        try:
            await self.client.sign_in(self.phone_number, self.verification_code)
            return "success"
        except SessionPasswordNeededError:
            return "2fa_required"
        except Exception as e:
            self.log_message(f"❌ Verify code error: {str(e)}")
            return "failed"

    def complete_login(self):
        """Complete login with 2FA password"""
        password = self.password_entry.get().strip()
        if not password:
            messagebox.showerror("Error", "Please enter your 2FA password")
            return

        self.password = password
        self.login_btn.config(state='disabled', text='Logging in...')
        self.log_message("🔐 Completing 2FA login...")

        # Complete login in thread
        threading.Thread(target=self.complete_login_thread, daemon=True).start()

    def complete_login_thread(self):
        """Complete 2FA login in separate thread"""
        try:
            # Make sure client and event loop are ready
            if not self.client:
                self.log_message("❌ Not connected to Telegram. Please connect first.")
                self.root.after(0, lambda: self.login_btn.config(state='normal', text='🚀 Login'))
                return

            # Wait for event loop
            max_wait = 50
            for i in range(max_wait):
                if self.event_loop is not None and self.event_loop.is_running():
                    break
                time.sleep(0.1)
            else:
                raise Exception("Event loop not ready")

            # Complete login using event loop
            future = asyncio.run_coroutine_threadsafe(
                self.complete_login_async(), self.event_loop)
            result = future.result(timeout=30)

            if result:
                self.log_message("✅ 2FA login successful!")
                self.root.after(0, self.on_login_success)
            else:
                self.log_message("❌ 2FA login failed")
                self.root.after(0, lambda: self.login_btn.config(state='normal', text='🚀 Login'))

        except Exception as e:
            self.log_message(f"❌ 2FA login failed: {str(e)}")
            self.root.after(0, lambda: self.login_btn.config(state='normal', text='🚀 Login'))

    async def complete_login_async(self):
        """Complete 2FA login (runs in event loop)"""
        try:
            await self.client.sign_in(password=self.password)
            return True
        except Exception as e:
            self.log_message(f"❌ 2FA login error: {str(e)}")
            return False

    def on_login_success(self):
        """Called when login is successful"""
        self.is_connected = True
        self.login_step = "connected"
        self.status_label.config(text="✅ Connected & Logged In", fg=self.colors['accent_green'])

        # Load groups
        self.log_message("📋 Loading your groups...")
        threading.Thread(target=self.load_groups_thread, daemon=True).start()

    def load_groups_thread(self):
        """Load groups in separate thread"""
        try:
            # Make sure client and event loop are ready
            if not self.client:
                self.log_message("❌ Not connected to Telegram. Please connect first.")
                return

            while self.event_loop is None:
                time.sleep(0.1)

            future = asyncio.run_coroutine_threadsafe(self.load_groups_async(), self.event_loop)
            groups = future.result(timeout=60)

            self.groups = groups
            self.root.after(0, self.update_groups_list)

        except Exception as e:
            self.log_message(f"❌ Failed to load groups: {str(e)}")

    async def load_groups_async(self):
        """Load groups asynchronously"""
        groups = []

        async for dialog in self.client.iter_dialogs():
            if dialog.is_group or dialog.is_channel:
                groups.append({
                    'name': dialog.name,
                    'id': dialog.id,
                    'entity': dialog.entity,
                    'participants': getattr(dialog.entity, 'participants_count', 0)
                })

        self.log_message(f"✅ Loaded {len(groups)} groups")
        return groups

    def update_groups_list(self):
        """Update the groups listbox"""
        self.groups_listbox.delete(0, tk.END)

        for group in self.groups:
            participants = group.get('participants', 0)
            display_name = f"{group['name']} ({participants} members)"
            self.groups_listbox.insert(tk.END, display_name)

        self.group_stats_label.config(text=f"{len(self.groups)} groups loaded • 0 selected")
        self.update_status_bar()

    # ============================================================================
    # BULK MESSAGING METHODS
    # ============================================================================

    def send_bulk_messages(self):
        """Send bulk messages to selected groups"""
        if not self.is_connected:
            messagebox.showerror("Error", "Please connect to Telegram first")
            return

        selected_indices = self.groups_listbox.curselection()
        if not selected_indices:
            messagebox.showerror("Error", "Please select at least one group")
            return

        # Get message components
        message_text = self.message_text.get(1.0, tk.END).strip()
        include_photo = self.include_photo.get()
        include_links = self.include_links.get()

        # Build final message
        final_message = ""
        if self.include_text.get() and message_text:
            final_message = message_text

        # Add links if enabled
        if include_links:
            links = self.links_text.get(1.0, tk.END).strip()
            if links:
                if final_message:
                    final_message += "\n\n🔗 Links:\n"
                final_message += links

        # Validate message
        if not final_message and not include_photo:
            messagebox.showerror("Error", "Please enter a message or select a photo")
            return

        if include_photo and not self.selected_photo_path:
            messagebox.showerror("Error", "Please select a photo")
            return

        selected_groups = [self.groups[i] for i in selected_indices]

        # Confirm sending
        confirm_msg = f"Send to {len(selected_groups)} groups?"
        if include_photo and self.selected_photo_path:
            confirm_msg += f"\n📷 Photo: {os.path.basename(self.selected_photo_path)}"
        if final_message:
            preview = final_message[:100] + ('...' if len(final_message) > 100 else '')
            confirm_msg += f"\n📝 Message: {preview}"

        if not messagebox.askyesno("Confirm Bulk Send", confirm_msg):
            return

        # Start sending
        self.send_once_btn.config(state='disabled', text='Sending...')
        self.log_message(f"🚀 Starting bulk send to {len(selected_groups)} groups")

        # Send in thread
        threading.Thread(target=self.send_messages_thread,
                        args=(selected_groups, final_message, include_photo), daemon=True).start()

    def send_messages_thread(self, groups, message, include_photo):
        """Send messages in separate thread"""
        try:
            future = asyncio.run_coroutine_threadsafe(
                self.send_messages_async(groups, message, include_photo), self.event_loop)
            future.result(timeout=300)  # 5 minute timeout

        except Exception as e:
            self.log_message(f"❌ Bulk send error: {str(e)}")
        finally:
            self.root.after(0, lambda: self.send_once_btn.config(state='normal', text='🚀 SEND ONCE'))

    async def send_messages_async(self, groups, message, include_photo):
        """Send messages asynchronously"""
        success_count = 0
        failed_count = 0

        for i, group in enumerate(groups):
            try:
                self.log_message(f"📤 Sending to {group['name']} ({i+1}/{len(groups)})")

                if include_photo and self.selected_photo_path:
                    # Send photo with message as caption
                    await self.client.send_file(
                        group['entity'],
                        self.selected_photo_path,
                        caption=message if message else None
                    )
                else:
                    # Send text message
                    await self.client.send_message(group['entity'], message)

                success_count += 1
                self.log_message(f"✅ Sent to {group['name']}")

                # Update stats
                self.campaign_stats['messages_sent'] += 1
                self.campaign_stats['success_count'] += 1
                self.campaign_stats['groups_reached'] = len(set([g['id'] for g in groups[:i+1]]))

                # Small delay between messages
                await asyncio.sleep(1.5)

            except FloodWaitError as e:
                self.log_message(f"⏳ Rate limited for {e.seconds} seconds: {group['name']}")
                failed_count += 1
                self.campaign_stats['failed_count'] += 1

            except Exception as e:
                self.log_message(f"❌ Failed to send to {group['name']}: {str(e)}")
                failed_count += 1
                self.campaign_stats['failed_count'] += 1

        # Final results
        success_rate = (success_count / len(groups)) * 100 if groups else 0
        self.log_message(f"📊 BULK SEND COMPLETE")
        self.log_message(f"✅ Successful: {success_count}")
        self.log_message(f"❌ Failed: {failed_count}")
        self.log_message(f"📈 Success rate: {success_rate:.1f}%")

        # Update UI
        self.root.after(0, self.update_campaign_stats)
        self.root.after(0, self.update_status_bar)

    def update_campaign_stats(self):
        """Update campaign statistics display"""
        stats = self.campaign_stats
        success_rate = (stats['success_count'] / max(stats['messages_sent'], 1)) * 100

        stats_text = f"Messages: {stats['messages_sent']} • Success: {success_rate:.1f}% • Groups: {stats['groups_reached']}"
        self.campaign_stats_label.config(text=stats_text)

    def update_status_bar(self):
        """Update the bottom status bar"""
        stats = self.campaign_stats
        success_rate = (stats['success_count'] / max(stats['messages_sent'], 1)) * 100

        status_text = f"Ready • {len(self.groups)} groups loaded • {stats['messages_sent']} messages sent • {success_rate:.1f}% success rate"
        self.stats_label.config(text=status_text)

    # ============================================================================
    # UI HELPER METHODS
    # ============================================================================

    def on_photo_toggle(self):
        """Handle photo checkbox toggle"""
        if self.include_photo.get():
            self.photo_frame.pack(fill=tk.X, pady=5)
        else:
            self.photo_frame.pack_forget()

    def on_links_toggle(self):
        """Handle links checkbox toggle"""
        if self.include_links.get():
            self.links_frame.pack(fill=tk.X, pady=5)
        else:
            self.links_frame.pack_forget()

    def on_automation_toggle(self):
        """Handle automation checkbox toggle"""
        if self.enable_automation.get():
            self.automation_settings_frame.pack(fill=tk.X, pady=5)
        else:
            self.automation_settings_frame.pack_forget()

    def select_photo(self):
        """Select a photo file"""
        file_path = filedialog.askopenfilename(
            title="Select Photo",
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.gif *.bmp")]
        )

        if file_path:
            self.selected_photo_path = file_path
            filename = os.path.basename(file_path)
            self.photo_label.config(text=f"Selected: {filename}")
            self.log_message(f"📷 Photo selected: {filename}")

    def insert_template(self, template):
        """Insert a message template"""
        self.message_text.delete(1.0, tk.END)
        self.message_text.insert(1.0, template)
        self.update_char_count()

    def update_char_count(self, event=None):
        """Update character count"""
        text = self.message_text.get(1.0, tk.END)
        char_count = len(text) - 1  # Subtract 1 for the newline
        self.char_count_label.config(text=f"{char_count}/4096 characters")

    def refresh_groups(self):
        """Refresh the groups list"""
        if not self.is_connected:
            messagebox.showwarning("Warning", "Please connect to Telegram first")
            return

        self.log_message("🔄 Refreshing groups...")
        threading.Thread(target=self.load_groups_thread, daemon=True).start()

    def select_all_groups(self):
        """Select all groups"""
        self.groups_listbox.select_set(0, tk.END)
        selected_count = len(self.groups_listbox.curselection())
        self.group_stats_label.config(text=f"{len(self.groups)} groups loaded • {selected_count} selected")

    def clear_selection(self):
        """Clear group selection"""
        self.groups_listbox.selection_clear(0, tk.END)
        self.group_stats_label.config(text=f"{len(self.groups)} groups loaded • 0 selected")

    def start_automated_sending(self):
        """Start automated 24/7 sending"""
        if not self.is_connected:
            messagebox.showerror("Error", "Please connect to Telegram first")
            return

        selected_indices = self.groups_listbox.curselection()
        if not selected_indices:
            messagebox.showerror("Error", "Please select at least one group")
            return

        # Get automation settings
        try:
            interval_minutes = int(self.interval_var.get())
            if interval_minutes < 5:
                messagebox.showerror("Error", "Minimum interval is 5 minutes")
                return
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid interval in minutes")
            return

        repeat_count = self.repeat_var.get()
        if repeat_count != "∞":
            try:
                repeat_count = int(repeat_count)
                if repeat_count < 1:
                    messagebox.showerror("Error", "Repeat count must be at least 1")
                    return
            except ValueError:
                messagebox.showerror("Error", "Please enter a valid repeat count or ∞")
                return

        # Confirm automation
        confirm_msg = f"Start 24/7 automation?\n\n"
        confirm_msg += f"📤 Groups: {len(selected_indices)}\n"
        confirm_msg += f"⏱️ Interval: {interval_minutes} minutes\n"
        confirm_msg += f"🔄 Repeats: {repeat_count}\n\n"
        confirm_msg += "⚠️ This will run continuously until stopped!"

        if not messagebox.askyesno("Confirm Automation", confirm_msg):
            return

        # Start automation
        self.automation_running = True
        self.send_auto_btn.config(state='disabled', text='🔄 RUNNING...')
        self.automation_status.config(text="🟢 RUNNING", fg=self.colors['accent_green'])

        selected_groups = [self.groups[i] for i in selected_indices]

        # Start automation thread
        threading.Thread(target=self.automation_loop_thread,
                        args=(selected_groups, interval_minutes, repeat_count), daemon=True).start()

        self.log_message(f"🔄 Started 24/7 automation: {len(selected_groups)} groups, {interval_minutes}min interval")

    def automation_loop_thread(self, groups, interval_minutes, repeat_count):
        """Run automation loop in separate thread"""
        count = 0

        while self.automation_running:
            try:
                # Check if we've reached repeat limit
                if repeat_count != "∞" and count >= repeat_count:
                    self.log_message(f"🔄 Automation completed: {count} cycles")
                    break

                count += 1
                self.log_message(f"🔄 Automation cycle {count} starting...")

                # Get current message content
                message_text = self.message_text.get(1.0, tk.END).strip()
                include_photo = self.include_photo.get()
                include_links = self.include_links.get()

                # Build message
                final_message = ""
                if self.include_text.get() and message_text:
                    final_message = message_text

                if include_links:
                    links = self.links_text.get(1.0, tk.END).strip()
                    if links:
                        if final_message:
                            final_message += "\n\n🔗 Links:\n"
                        final_message += links

                # Send messages
                if final_message or include_photo:
                    future = asyncio.run_coroutine_threadsafe(
                        self.send_messages_async(groups, final_message, include_photo), self.event_loop)
                    future.result(timeout=300)

                # Wait for next cycle (unless stopping)
                for i in range(interval_minutes * 60):  # Convert to seconds
                    if not self.automation_running:
                        break
                    time.sleep(1)

            except Exception as e:
                self.log_message(f"❌ Automation error: {str(e)}")
                time.sleep(60)  # Wait 1 minute on error

        # Automation stopped
        self.automation_running = False
        self.root.after(0, lambda: self.send_auto_btn.config(state='normal', text='🔄 START AUTOMATION'))
        self.root.after(0, lambda: self.automation_status.config(text="🔴 STOPPED", fg=self.colors['accent_red']))
        self.log_message("🔄 Automation stopped")

    # ============================================================================
    # AUTOMATION METHODS
    # ============================================================================

    def start_automation(self):
        """Start automation"""
        if not self.is_connected:
            messagebox.showerror("Error", "Please connect to Telegram first")
            return

        if self.automation_running:
            messagebox.showinfo("Info", "Automation is already running")
            return

        self.automation_running = True
        self.automation_status.config(text="🟢 RUNNING", fg=self.colors['accent_green'])
        self.log_message("🔄 Automation started")

        # Start automation loop
        threading.Thread(target=self.automation_loop, daemon=True).start()

    def stop_automation(self):
        """Stop automation"""
        self.automation_running = False
        self.automation_status.config(text="🔴 STOPPED", fg=self.colors['accent_red'])
        self.log_message("🔄 Automation stopped")

    def automation_loop(self):
        """Main automation loop"""
        while self.automation_running:
            try:
                # Check for pinned messages to comment on
                if self.auto_comment.get():
                    self.check_pinned_messages()

                # Auto-discovery if enabled
                if self.discovery_running:
                    self.discover_groups()

                # Wait before next cycle
                time.sleep(30)  # 30 second cycle

            except Exception as e:
                self.log_message(f"❌ Automation error: {str(e)}")
                time.sleep(60)  # Wait longer on error

    def check_pinned_messages(self):
        """Check for pinned messages and calls posts to comment on"""
        try:
            if not self.auto_comment_calls.get():
                return

            future = asyncio.run_coroutine_threadsafe(self.check_calls_and_comment(), self.event_loop)
            future.result(timeout=120)

        except Exception as e:
            self.log_message(f"❌ Error checking calls: {str(e)}")

    async def check_calls_and_comment(self):
        """Check for calls posts and comment on them"""
        try:
            comment_template = self.comment_template_text.get(1.0, tk.END).strip()
            if not comment_template:
                return

            # Split template into individual comments
            comments = [c.strip() for c in comment_template.split('\n') if c.strip()]
            if not comments:
                return

            # Check recent messages in groups for "calls"
            calls_keywords = ['call', 'calls', 'signal', 'alert', 'pump', 'gem', 'moon', 'x100', 'x10']

            for group in self.groups[:10]:  # Check first 10 groups to avoid rate limits
                try:
                    # Get recent messages
                    messages = await self.client.get_messages(group['entity'], limit=5)

                    for message in messages:
                        if not message.text:
                            continue

                        text_lower = message.text.lower()

                        # Check if message contains call keywords
                        if any(keyword in text_lower for keyword in calls_keywords):
                            # Check if message is recent (last 30 minutes)
                            import datetime
                            now = datetime.datetime.now(datetime.timezone.utc)
                            if message.date and (now - message.date).total_seconds() < 1800:  # 30 minutes

                                # Pick random comment from template
                                import random
                                comment = random.choice(comments)

                                try:
                                    # Reply to the message
                                    await self.client.send_message(
                                        group['entity'],
                                        comment,
                                        reply_to=message.id
                                    )

                                    self.log_message(f"💬 Commented on call in {group['name']}: {comment[:50]}...")

                                    # Small delay between comments
                                    await asyncio.sleep(3)

                                except Exception as e:
                                    self.log_message(f"❌ Failed to comment in {group['name']}: {str(e)}")

                    # Delay between groups
                    await asyncio.sleep(2)

                except Exception as e:
                    self.log_message(f"❌ Error checking {group['name']}: {str(e)}")
                    continue

        except Exception as e:
            self.log_message(f"❌ Error in calls checking: {str(e)}")

    # ============================================================================
    # GROUP DISCOVERY METHODS
    # ============================================================================

    def start_discovery(self):
        """Start group discovery"""
        if not self.is_connected:
            messagebox.showerror("Error", "Please connect to Telegram first")
            return

        if self.discovery_running:
            messagebox.showinfo("Info", "Discovery is already running")
            return

        self.discovery_running = True
        self.discovery_status.config(text="🟢 Discovery: RUNNING", fg=self.colors['accent_green'])
        self.log_message("🔍 Group discovery started")

        # Start discovery in thread
        threading.Thread(target=self.discovery_loop, daemon=True).start()

    def stop_discovery(self):
        """Stop group discovery"""
        self.discovery_running = False
        self.discovery_status.config(text="🔴 Discovery: STOPPED", fg=self.colors['accent_red'])
        self.log_message("🔍 Group discovery stopped")

    def discovery_loop(self):
        """Main discovery loop"""
        while self.discovery_running:
            try:
                keywords = self.keywords_text.get(1.0, tk.END).strip().split(',')
                keywords = [k.strip() for k in keywords if k.strip()]

                if keywords:
                    self.discover_groups_with_keywords(keywords)

                # Wait before next discovery cycle
                time.sleep(300)  # 5 minute cycle

            except Exception as e:
                self.log_message(f"❌ Discovery error: {str(e)}")
                time.sleep(600)  # Wait longer on error

    def discover_groups_with_keywords(self, keywords):
        """Discover groups using keywords"""
        try:
            future = asyncio.run_coroutine_threadsafe(
                self.search_groups_async(keywords), self.event_loop)
            new_groups = future.result(timeout=120)

            if new_groups:
                self.log_message(f"🔍 Found {len(new_groups)} new groups")

                # Auto-join if enabled
                if self.auto_join_discovered.get():
                    self.join_discovered_groups(new_groups)

                # Update stats
                self.update_discovery_stats()

        except Exception as e:
            self.log_message(f"❌ Discovery search error: {str(e)}")

    async def search_groups_async(self, keywords):
        """Search for groups asynchronously"""
        found_groups = []

        for keyword in keywords[:3]:  # Limit to avoid rate limits
            try:
                self.log_message(f"🔍 Searching for: {keyword}")

                # Search through dialogs for groups with keyword in name
                async for dialog in self.client.iter_dialogs():
                    if not (dialog.is_group or dialog.is_channel):
                        continue

                    if keyword.lower() in dialog.name.lower():
                        # Check if we're already a member
                        try:
                            participants = await self.client.get_participants(dialog.entity, limit=1)
                            is_member = True
                        except:
                            is_member = False

                        if not is_member:
                            found_groups.append({
                                'name': dialog.name,
                                'entity': dialog.entity,
                                'keyword': keyword
                            })

                            if len(found_groups) >= 10:  # Limit results
                                break

                await asyncio.sleep(2)  # Delay between searches

            except Exception as e:
                self.log_message(f"❌ Search error for '{keyword}': {str(e)}")
                continue

        return found_groups

    def join_discovered_groups(self, groups):
        """Join discovered groups"""
        for group in groups:
            try:
                future = asyncio.run_coroutine_threadsafe(
                    self.join_group_async(group), self.event_loop)
                success = future.result(timeout=30)

                if success:
                    self.log_message(f"🤝 Joined: {group['name']}")
                    self.discovered_groups.append(group)
                else:
                    self.log_message(f"❌ Failed to join: {group['name']}")

                time.sleep(3)  # Delay between joins

            except Exception as e:
                self.log_message(f"❌ Join error for {group['name']}: {str(e)}")

    async def join_group_async(self, group):
        """Join a group asynchronously"""
        try:
            await self.client(JoinChannelRequest(group['entity']))
            return True
        except UserAlreadyParticipantError:
            return True  # Already a member
        except Exception as e:
            return False

    def update_discovery_stats(self):
        """Update discovery statistics"""
        found_count = len(self.discovered_groups)
        joined_count = len([g for g in self.discovered_groups if 'joined' in g])

        self.disc_stats_found.config(text=f"Groups Found: {found_count}")
        self.disc_stats_joined.config(text=f"Groups Joined: {joined_count}")

    # ============================================================================
    # MONITOR METHODS
    # ============================================================================

    def start_monitor(self):
        """Start monitoring"""
        self.log_message("▶️ Monitor started")

    def pause_monitor(self):
        """Pause monitoring"""
        self.log_message("⏸️ Monitor paused")

    def clear_monitor(self):
        """Clear monitor log"""
        self.monitor_text.delete(1.0, tk.END)
        self.monitor_text.insert(tk.END, "🚀 PumpX Shill Bot Monitor Ready\n")
        self.monitor_text.insert(tk.END, "=" * 50 + "\n")

    def emergency_stop(self):
        """Emergency stop all operations"""
        self.automation_running = False
        self.discovery_running = False
        self.automation_status.config(text="🔴 STOPPED", fg=self.colors['accent_red'])
        self.discovery_status.config(text="🔴 Discovery: STOPPED", fg=self.colors['accent_red'])
        self.log_message("🛑 EMERGENCY STOP - All operations halted")
        messagebox.showwarning("Emergency Stop", "All operations have been stopped!")
    
    def setup_middle_column(self):
        """Setup middle column - Live Monitor & Groups"""
        # Live Monitor Card
        self.create_monitor_card()
        
        # Groups Manager Card
        self.create_groups_card()
    
    def setup_right_column(self):
        """Setup right column - Automation & Discovery"""
        # Automation Control Card
        self.create_automation_card()
        
        # Group Discovery Card
        self.create_discovery_card()
    
    def start_event_loop(self):
        """Start the asyncio event loop in a separate thread"""
        def run_event_loop():
            try:
                self.event_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self.event_loop)
                print("✅ Event loop ready")
                self.event_loop.run_forever()
            except Exception as e:
                print(f"❌ Error in event loop: {e}")

        self.event_loop_thread = threading.Thread(target=run_event_loop, daemon=True)
        self.event_loop_thread.start()

        # Wait for loop to start properly
        for i in range(50):  # Increased wait time
            time.sleep(0.1)
            if self.event_loop is not None and self.event_loop.is_running():
                break

        if self.event_loop and self.event_loop.is_running():
            self.log_message("✅ Event loop started successfully")
        else:
            self.log_message("❌ Failed to start event loop properly")
    
    def log_message(self, message):
        """Log message to monitor (thread-safe)"""
        timestamp = time.strftime('%H:%M:%S')
        log_entry = f"{timestamp} - {message}"

        # Print to console immediately
        print(log_entry)

        # Update GUI safely from main thread
        if hasattr(self, 'monitor_text') and self.monitor_text:
            try:
                # Use root.after to safely update GUI from any thread
                self.root.after(0, lambda: self._update_monitor_safe(log_entry))
            except:
                pass  # Ignore GUI update errors

    def _update_monitor_safe(self, log_entry):
        """Safely update monitor from main thread"""
        try:
            if hasattr(self, 'monitor_text') and self.monitor_text:
                self.monitor_text.insert(tk.END, log_entry + "\n")
                self.monitor_text.see(tk.END)
        except:
            pass  # Ignore any GUI errors
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = PumpXShillBot()
    app.run()
