#!/usr/bin/env python3
"""
Telegram DM Bot - Windows GUI Application for Bulk Messaging
"""
import asyncio
import os
import sys
import webbrowser
import threading
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from typing import List, Dict, Optional
from telethon import TelegramClient
from telethon.tl.types import <PERSON>r, Chat, Channel
from telethon.errors import FloodWaitError, PeerFloodError, UserPrivacyRestrictedError
import time

class TelegramDMBotGUI:
    def __init__(self):
        self.client = None
        self.api_id = 23806500
        self.api_hash = "c9330b83e3e23125e890eaa41902b193"
        self.session_name = "telegram_dm_session"
        self.contacts = []
        self.groups = []
        self.is_connected = False
        self.event_loop = None
        self.loop_thread = None

        # Create GUI
        self.root = tk.Tk()
        self.setup_gui()

        # Start event loop thread
        self.start_event_loop_thread()

    def start_event_loop_thread(self):
        """Start a dedicated event loop thread"""
        def run_event_loop():
            try:
                self.event_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self.event_loop)
                self.log_message("Event loop thread started")
                self.event_loop.run_forever()
            except Exception as e:
                self.log_message(f"Error in event loop thread: {e}")

        self.loop_thread = threading.Thread(target=run_event_loop, daemon=True)
        self.loop_thread.start()

        # Wait for loop to start
        import time
        for i in range(20):  # Wait up to 2 seconds
            time.sleep(0.1)
            if self.event_loop is not None:
                break

        if self.event_loop:
            self.log_message("✅ Dedicated event loop ready")
        else:
            self.log_message("❌ Failed to start event loop")

    def setup_gui(self):
        """Setup the GUI interface"""
        self.root.title("Telegram DM Bot - Bulk Messaging Tool")
        self.root.geometry("800x600")
        self.root.configure(bg='#2b2b2b')

        # Create main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title
        title_label = tk.Label(main_frame, text="🚀 TELEGRAM DM BOT",
                              font=("Arial", 20, "bold"),
                              bg='#2b2b2b', fg='#ffffff')
        title_label.pack(pady=10)

        subtitle_label = tk.Label(main_frame, text="Bulk Messaging Tool - Send to 1000+ Groups",
                                 font=("Arial", 12),
                                 bg='#2b2b2b', fg='#cccccc')
        subtitle_label.pack(pady=5)

        # Connection frame
        conn_frame = ttk.LabelFrame(main_frame, text="Connection Status", padding=10)
        conn_frame.pack(fill=tk.X, pady=10)

        self.status_label = tk.Label(conn_frame, text="❌ Not Connected",
                                    font=("Arial", 10), fg='red')
        self.status_label.pack(side=tk.LEFT)

        btn_frame = tk.Frame(conn_frame)
        btn_frame.pack(side=tk.RIGHT)

        self.test_btn = tk.Button(btn_frame, text="Test Connection",
                                 command=self.test_connection,
                                 bg='#FF9800', fg='white', font=("Arial", 9))
        self.test_btn.pack(side=tk.LEFT, padx=5)

        self.connect_btn = tk.Button(btn_frame, text="Connect to Telegram",
                                    command=self.connect_telegram,
                                    bg='#4CAF50', fg='white', font=("Arial", 10))
        self.connect_btn.pack(side=tk.LEFT)

        # Phone number frame (initially hidden)
        self.phone_frame = ttk.LabelFrame(main_frame, text="Step 1: Enter Phone Number", padding=10)

        tk.Label(self.phone_frame, text="Phone Number (with country code):").pack(anchor=tk.W)
        tk.Label(self.phone_frame, text="Examples: *************** or +1234567890",
                font=("Arial", 9), fg='gray').pack(anchor=tk.W)
        self.phone_entry = tk.Entry(self.phone_frame, font=("Arial", 12), width=25)
        self.phone_entry.pack(fill=tk.X, pady=5)
        self.phone_entry.insert(0, "+1 ")  # Pre-fill with US country code

        self.send_code_btn = tk.Button(self.phone_frame, text="Send Verification Code",
                                      command=self.send_verification_code,
                                      bg='#2196F3', fg='white', font=("Arial", 10))
        self.send_code_btn.pack(pady=10)

        # Code entry frame (initially hidden)
        self.code_frame = ttk.LabelFrame(main_frame, text="Step 2: Enter Verification Code", padding=10)

        self.code_status_label = tk.Label(self.code_frame, text="Verification code sent to your phone",
                                         fg='green', font=("Arial", 10))
        self.code_status_label.pack(anchor=tk.W, pady=5)

        tk.Label(self.code_frame, text="Enter the verification code:").pack(anchor=tk.W)
        self.code_entry = tk.Entry(self.code_frame, font=("Arial", 12), width=15)
        self.code_entry.pack(fill=tk.X, pady=5)

        self.login_btn = tk.Button(self.code_frame, text="Complete Login",
                                  command=self.complete_login,
                                  bg='#4CAF50', fg='white', font=("Arial", 10))
        self.login_btn.pack(pady=10)

        # Main content frame (initially hidden)
        self.main_content = ttk.Frame(main_frame)

        # Notebook for tabs
        self.notebook = ttk.Notebook(self.main_content)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Bulk messaging tab
        self.bulk_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.bulk_frame, text="📤 Bulk Messaging")

        # Groups selection
        groups_label_frame = ttk.LabelFrame(self.bulk_frame, text="Select Groups", padding=10)
        groups_label_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Groups listbox with scrollbar
        list_frame = tk.Frame(groups_label_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        scrollbar = tk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.groups_listbox = tk.Listbox(list_frame, selectmode=tk.MULTIPLE,
                                        yscrollcommand=scrollbar.set,
                                        font=("Arial", 10))
        self.groups_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.groups_listbox.yview)

        # Selection buttons
        btn_frame = tk.Frame(groups_label_frame)
        btn_frame.pack(fill=tk.X, pady=5)

        tk.Button(btn_frame, text="Select All", command=self.select_all_groups,
                 bg='#FF9800', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="Clear Selection", command=self.clear_selection,
                 bg='#f44336', fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="Refresh Groups", command=self.refresh_groups,
                 bg='#9C27B0', fg='white').pack(side=tk.LEFT, padx=5)

        # Message composition
        msg_frame = ttk.LabelFrame(self.bulk_frame, text="Compose Message", padding=10)
        msg_frame.pack(fill=tk.X, pady=5)

        self.message_text = scrolledtext.ScrolledText(msg_frame, height=6,
                                                     font=("Arial", 11))
        self.message_text.pack(fill=tk.X, pady=5)

        # Rate limiting
        rate_frame = ttk.LabelFrame(self.bulk_frame, text="Rate Limiting", padding=10)
        rate_frame.pack(fill=tk.X, pady=5)

        self.rate_var = tk.StringVar(value="1")
        rate_options = [("Fast (0.5s)", "0.5"), ("Normal (1s)", "1"),
                       ("Safe (2s)", "2"), ("Custom", "custom")]

        for text, value in rate_options:
            tk.Radiobutton(rate_frame, text=text, variable=self.rate_var,
                          value=value).pack(side=tk.LEFT, padx=10)

        self.custom_delay = tk.Entry(rate_frame, width=10)
        self.custom_delay.pack(side=tk.RIGHT)
        tk.Label(rate_frame, text="Custom delay (seconds):").pack(side=tk.RIGHT, padx=5)

        # Send button
        self.send_btn = tk.Button(self.bulk_frame, text="🚀 SEND BULK MESSAGES",
                                 command=self.send_bulk_messages,
                                 bg='#4CAF50', fg='white',
                                 font=("Arial", 14, "bold"), height=2)
        self.send_btn.pack(fill=tk.X, pady=10)

        # Progress frame
        self.progress_frame = ttk.LabelFrame(self.bulk_frame, text="Progress", padding=10)

        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=5)

        self.progress_label = tk.Label(self.progress_frame, text="Ready to send messages")
        self.progress_label.pack()

        # Statistics tab
        self.stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.stats_frame, text="📊 Statistics")

        self.stats_text = scrolledtext.ScrolledText(self.stats_frame,
                                                   font=("Courier", 11))
        self.stats_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Log tab
        self.log_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.log_frame, text="📝 Logs")

        self.log_text = scrolledtext.ScrolledText(self.log_frame,
                                                 font=("Courier", 10))
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        

    
    def log_message(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n")
        self.log_text.see(tk.END)
        self.root.update()

    def test_connection(self):
        """Test internet connection and API credentials"""
        self.test_btn.config(state='disabled', text='Testing...')
        self.log_message("Testing connection...")

        thread = threading.Thread(target=self.test_connection_thread)
        thread.daemon = True
        thread.start()

    def test_connection_thread(self):
        """Test connection in thread"""
        try:
            import socket
            import urllib.request

            # Test internet connection
            self.log_message("Testing internet connection...")
            try:
                socket.create_connection(("8.8.8.8", 53), timeout=5)
                self.log_message("✅ Internet connection: OK")
            except:
                self.log_message("❌ Internet connection: FAILED")
                self.root.after(0, lambda: messagebox.showerror("Connection Test",
                    "No internet connection detected. Please check your network."))
                self.root.after(0, lambda: self.test_btn.config(state='normal', text='Test Connection'))
                return

            # Test Telegram servers
            self.log_message("Testing Telegram servers...")
            try:
                socket.create_connection(("149.154.167.50", 443), timeout=10)
                self.log_message("✅ Telegram servers: Reachable")
            except:
                self.log_message("❌ Telegram servers: Not reachable")
                self.root.after(0, lambda: messagebox.showwarning("Connection Test",
                    "Cannot reach Telegram servers. This might be due to firewall or network restrictions."))

            # Test API credentials format
            self.log_message("Testing API credentials format...")
            if self.api_id and isinstance(self.api_id, int) and self.api_id > 0:
                self.log_message("✅ API ID format: Valid")
            else:
                self.log_message("❌ API ID format: Invalid")

            if self.api_hash and isinstance(self.api_hash, str) and len(self.api_hash) == 32:
                self.log_message("✅ API Hash format: Valid")
            else:
                self.log_message("❌ API Hash format: Invalid")

            self.log_message("Connection test completed. Check logs for details.")
            self.root.after(0, lambda: messagebox.showinfo("Connection Test",
                "Connection test completed. Check the logs tab for detailed results."))

        except Exception as e:
            self.log_message(f"Test error: {str(e)}")
        finally:
            self.root.after(0, lambda: self.test_btn.config(state='normal', text='Test Connection'))

    def connect_telegram(self):
        """Start connection process"""
        self.connect_btn.config(state='disabled', text='Connecting...')
        self.log_message("Starting connection to Telegram...")

        # Run connection in thread
        thread = threading.Thread(target=self.connect_thread)
        thread.daemon = True
        thread.start()

    def connect_thread(self):
        """Connection thread"""
        try:
            # Check if event loop is ready
            if not self.event_loop:
                self.log_message("❌ Event loop not ready, retrying...")
                import time
                time.sleep(1)
                if not self.event_loop:
                    raise Exception("Event loop failed to start")

            # Use the dedicated event loop
            future = asyncio.run_coroutine_threadsafe(self.connect_async(), self.event_loop)
            future.result(timeout=30)  # Wait for result with timeout
        except Exception as e:
            self.log_message(f"Connection error: {str(e)}")
            self.root.after(0, lambda: self.connect_btn.config(state='normal', text='Connect to Telegram'))

    async def connect_async(self):
        """Connect to Telegram"""
        try:
            self.log_message(f"Using API ID: {self.api_id}")
            self.log_message(f"Using API Hash: {self.api_hash[:10]}...")

            self.client = TelegramClient(self.session_name, self.api_id, self.api_hash)
            self.log_message("Connecting to Telegram servers...")

            # Connect to Telegram with timeout
            await asyncio.wait_for(self.client.connect(), timeout=30)
            self.log_message("Connected to Telegram servers successfully!")

            # Check if already logged in
            if await self.client.is_user_authorized():
                me = await self.client.get_me()
                self.log_message(f"Already logged in as: {me.first_name}")
                self.root.after(0, self.on_login_success)
                return True
            else:
                self.log_message("Connected! Ready for phone number login.")
                self.root.after(0, self.show_phone_input)
                return True

        except asyncio.TimeoutError:
            self.log_message("Connection timeout! Check your internet connection.")
            self.root.after(0, lambda: messagebox.showerror("Connection Error",
                "Connection timeout! Please check your internet connection and try again."))
            self.root.after(0, lambda: self.connect_btn.config(state='normal', text='Connect to Telegram'))
            return False
        except Exception as e:
            error_msg = str(e)
            self.log_message(f"Connection failed: {error_msg}")

            # Specific error messages
            if "api_id" in error_msg.lower() or "api_hash" in error_msg.lower():
                self.root.after(0, lambda: messagebox.showerror("API Error",
                    "Invalid API credentials. Please check your API ID and Hash."))
            elif "network" in error_msg.lower() or "connection" in error_msg.lower():
                self.root.after(0, lambda: messagebox.showerror("Network Error",
                    "Network connection failed. Please check your internet connection."))
            else:
                self.root.after(0, lambda: messagebox.showerror("Connection Error",
                    f"Failed to connect: {error_msg}"))

            self.root.after(0, lambda: self.connect_btn.config(state='normal', text='Connect to Telegram'))
            return False

    def show_phone_input(self):
        """Show phone number input"""
        self.connect_btn.config(state='disabled', text='Connected ✅')
        self.phone_frame.pack(fill=tk.X, pady=10)

    def send_verification_code(self):
        """Send verification code to phone"""
        phone_raw = self.phone_entry.get().strip()

        if not phone_raw:
            messagebox.showerror("Error", "Please enter your phone number")
            return

        # Clean up phone number - remove spaces, dashes, parentheses
        phone = phone_raw.replace(' ', '').replace('-', '').replace('(', '').replace(')', '').replace('.', '')

        self.log_message(f"Original input: {phone_raw}")
        self.log_message(f"Cleaned number: {phone}")

        # Validate phone number format
        if not phone.startswith('+'):
            messagebox.showerror("Error", "Phone number must start with + and include country code\n\nExamples:\n*************** (US)\n+44 7123 456789 (UK)\n+33 1 23 45 67 89 (France)")
            return

        # Remove + and check if rest are digits
        phone_digits = phone[1:]
        if not phone_digits.isdigit():
            messagebox.showerror("Error", f"Phone number can only contain +, digits, spaces, and dashes\n\nYour cleaned number: {phone}\nPlease check for invalid characters")
            return

        if len(phone_digits) < 7 or len(phone_digits) > 15:
            messagebox.showerror("Error", f"Phone number should have 7-15 digits after country code\n\nYour number has {len(phone_digits)} digits: {phone}")
            return

        self.log_message(f"Phone number validation passed: {phone}")

        # Update the entry field with cleaned number for user reference
        self.phone_entry.delete(0, tk.END)
        self.phone_entry.insert(0, phone)

        self.send_code_btn.config(state='disabled', text='Sending code...')
        self.log_message(f"Sending verification code to {phone}")

        # Run in thread
        thread = threading.Thread(target=self.send_code_thread, args=(phone,))
        thread.daemon = True
        thread.start()

    def send_code_thread(self, phone):
        """Send code in thread"""
        try:
            # Check if event loop is ready
            if not self.event_loop:
                raise Exception("Event loop not ready")

            # Use the dedicated event loop
            future = asyncio.run_coroutine_threadsafe(self.send_code_async(phone), self.event_loop)
            future.result(timeout=30)  # Wait for result with timeout
        except Exception as e:
            self.log_message(f"Error sending code: {str(e)}")
            self.root.after(0, lambda: self.send_code_btn.config(state='normal', text='Send Verification Code'))

    async def send_code_async(self, phone):
        """Send verification code async"""
        try:
            self.log_message(f"Requesting verification code for {phone}")

            # Send code request with timeout
            result = await asyncio.wait_for(self.client.send_code_request(phone), timeout=30)

            self.log_message("Verification code request sent successfully!")
            self.log_message(f"Code type: {result.type}")

            if hasattr(result, 'next_type') and result.next_type:
                self.log_message(f"Alternative method available: {result.next_type}")

            self.log_message("Check your phone for the verification code (SMS or Telegram app)")
            self.root.after(0, self.show_code_input)

        except asyncio.TimeoutError:
            self.log_message("Timeout while sending verification code")
            self.root.after(0, lambda: messagebox.showerror("Timeout Error",
                "Timeout while sending verification code. Please try again."))
            self.root.after(0, lambda: self.send_code_btn.config(state='normal', text='Send Verification Code'))
        except Exception as e:
            error_msg = str(e)
            self.log_message(f"Failed to send code: {error_msg}")

            # Specific error messages for common issues
            if "phone number invalid" in error_msg.lower():
                self.root.after(0, lambda: messagebox.showerror("Invalid Phone Number",
                    "Invalid phone number format. Please use international format with country code (e.g., +1234567890)"))
            elif "phone number banned" in error_msg.lower():
                self.root.after(0, lambda: messagebox.showerror("Phone Banned",
                    "This phone number is banned from Telegram. Please use a different number."))
            elif "flood" in error_msg.lower():
                self.root.after(0, lambda: messagebox.showerror("Rate Limited",
                    "Too many requests. Please wait a few minutes before trying again."))
            else:
                self.root.after(0, lambda: messagebox.showerror("Error",
                    f"Failed to send verification code: {error_msg}"))

            self.root.after(0, lambda: self.send_code_btn.config(state='normal', text='Send Verification Code'))

    def show_code_input(self):
        """Show code input after code is sent"""
        self.send_code_btn.config(state='disabled', text='Code Sent ✅')
        self.code_frame.pack(fill=tk.X, pady=10)
        self.code_entry.focus()

    def complete_login(self):
        """Complete login with verification code"""
        phone = self.phone_entry.get().strip()
        code = self.code_entry.get().strip()

        if not code:
            messagebox.showerror("Error", "Please enter the verification code")
            return

        self.login_btn.config(state='disabled', text='Logging in...')
        self.log_message(f"Completing login with verification code...")

        # Run in thread
        thread = threading.Thread(target=self.complete_login_thread, args=(phone, code))
        thread.daemon = True
        thread.start()

    def complete_login_thread(self, phone, code):
        """Complete login in thread"""
        try:
            # Use the dedicated event loop
            future = asyncio.run_coroutine_threadsafe(self.complete_login_async(phone, code), self.event_loop)
            future.result(timeout=30)  # Wait for result with timeout
        except Exception as e:
            self.log_message(f"Login error: {str(e)}")
            self.root.after(0, lambda: self.login_btn.config(state='normal', text='Complete Login'))

    async def complete_login_async(self, phone, code):
        """Complete login async"""
        try:
            await self.client.sign_in(phone, code)
            me = await self.client.get_me()
            self.log_message(f"Successfully logged in as: {me.first_name}")
            self.root.after(0, self.on_login_success)

        except Exception as e:
            self.log_message(f"Login failed: {str(e)}")
            if "phone code invalid" in str(e).lower():
                messagebox.showerror("Error", "Invalid verification code. Please try again.")
            elif "phone code expired" in str(e).lower():
                messagebox.showerror("Error", "Verification code expired. Please request a new one.")
                self.root.after(0, self.reset_to_phone_input)
            else:
                messagebox.showerror("Error", f"Login failed: {str(e)}")
            self.root.after(0, lambda: self.login_btn.config(state='normal', text='Complete Login'))

    def reset_to_phone_input(self):
        """Reset to phone input if code expired"""
        self.code_frame.pack_forget()
        self.send_code_btn.config(state='normal', text='Send Verification Code')
        self.code_entry.delete(0, tk.END)

    def on_login_success(self):
        """Called when login is successful"""
        self.is_connected = True
        self.status_label.config(text="✅ Connected & Logged In", fg='green')
        self.phone_frame.pack_forget()
        self.code_frame.pack_forget()
        self.main_content.pack(fill=tk.BOTH, expand=True, pady=10)
        self.refresh_groups()
    
    def refresh_groups(self):
        """Refresh groups list"""
        if not self.is_connected:
            return

        self.log_message("Loading groups...")
        thread = threading.Thread(target=self.refresh_groups_thread)
        thread.daemon = True
        thread.start()

    def refresh_groups_thread(self):
        """Refresh groups in thread"""
        try:
            # Use the dedicated event loop
            future = asyncio.run_coroutine_threadsafe(self.get_contacts_async(), self.event_loop)
            future.result(timeout=30)  # Wait for result with timeout
        except Exception as e:
            self.log_message(f"Error loading groups: {str(e)}")

    async def get_contacts_async(self):
        """Get all contacts and chats"""
        self.log_message("Loading your contacts and chats...")
        contacts = []
        groups = []

        try:
            async for dialog in self.client.iter_dialogs():
                contact_type = "Unknown"
                if isinstance(dialog.entity, User):
                    if not dialog.entity.bot:
                        contact_type = "User"
                    else:
                        contact_type = "Bot"
                elif isinstance(dialog.entity, Chat):
                    contact_type = "Group"
                elif isinstance(dialog.entity, Channel):
                    contact_type = "Channel" if dialog.entity.broadcast else "Supergroup"

                contact = {
                    'id': dialog.entity.id,
                    'name': dialog.name or "Unknown",
                    'type': contact_type,
                    'entity': dialog.entity
                }

                contacts.append(contact)

                # Add to groups list if it's a group
                if contact_type in ['Group', 'Supergroup']:
                    groups.append(contact)

            self.contacts = contacts
            self.groups = groups

            self.log_message(f"Loaded {len(contacts)} contacts ({len(groups)} groups)")

            # Update GUI
            self.root.after(0, self.update_groups_list)
            self.root.after(0, self.update_statistics)

        except Exception as e:
            self.log_message(f"Failed to load contacts: {str(e)}")

    def update_groups_list(self):
        """Update groups listbox"""
        self.groups_listbox.delete(0, tk.END)
        for group in self.groups:
            self.groups_listbox.insert(tk.END, f"{group['name']} ({group['type']})")

    def update_statistics(self):
        """Update statistics tab"""
        users = [c for c in self.contacts if c['type'] == 'User']
        groups = [c for c in self.contacts if c['type'] in ['Group', 'Supergroup']]
        channels = [c for c in self.contacts if c['type'] == 'Channel']
        bots = [c for c in self.contacts if c['type'] == 'Bot']

        stats = f"""📊 CONTACT STATISTICS
{'='*50}
👥 Users: {len(users)}
👨‍👩‍👧‍👦 Groups: {len(groups)}
📢 Channels: {len(channels)}
🤖 Bots: {len(bots)}
📱 Total: {len(self.contacts)}
{'='*50}

🎯 BULK MESSAGING POTENTIAL:
• You can send to {len(groups)} groups at once
• Estimated time (1s delay): {len(groups)} seconds
• Estimated time (2s delay): {len(groups)*2} seconds
"""

        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats)

    def select_all_groups(self):
        """Select all groups"""
        self.groups_listbox.select_set(0, tk.END)

    def clear_selection(self):
        """Clear group selection"""
        self.groups_listbox.selection_clear(0, tk.END)
    
    def send_bulk_messages(self):
        """Send bulk messages"""
        selected_indices = self.groups_listbox.curselection()
        if not selected_indices:
            messagebox.showerror("Error", "Please select at least one group")
            return

        message = self.message_text.get(1.0, tk.END).strip()
        if not message:
            messagebox.showerror("Error", "Please enter a message")
            return

        selected_groups = [self.groups[i] for i in selected_indices]

        # Confirm sending
        result = messagebox.askyesno("Confirm",
                                   f"Send message to {len(selected_groups)} groups?\n\n"
                                   f"Message preview:\n{message[:100]}...")
        if not result:
            return

        # Get delay
        delay = 1.0
        if self.rate_var.get() == "custom":
            try:
                delay = float(self.custom_delay.get())
            except:
                delay = 1.0
        else:
            delay = float(self.rate_var.get())

        # Disable send button
        self.send_btn.config(state='disabled', text='Sending...')
        self.progress_frame.pack(fill=tk.X, pady=5)

        # Start sending in thread
        thread = threading.Thread(target=self.send_messages_thread,
                                 args=(selected_groups, message, delay))
        thread.daemon = True
        thread.start()

    def send_messages_thread(self, groups, message, delay):
        """Send messages in thread"""
        try:
            # Use the dedicated event loop
            future = asyncio.run_coroutine_threadsafe(self.send_messages_async(groups, message, delay), self.event_loop)
            future.result(timeout=300)  # Longer timeout for bulk sending
        except Exception as e:
            self.log_message(f"Sending error: {str(e)}")
        finally:
            self.root.after(0, lambda: self.send_btn.config(state='normal', text='🚀 SEND BULK MESSAGES'))

    async def send_messages_async(self, groups, message, delay):
        """Send messages async"""
        successful = 0
        failed = 0
        total = len(groups)

        self.root.after(0, lambda: self.progress_bar.config(maximum=total, value=0))

        for i, group in enumerate(groups, 1):
            try:
                self.root.after(0, lambda i=i, name=group['name']:
                               self.progress_label.config(text=f"Sending to {name} ({i}/{total})"))

                await self.client.send_message(group['entity'], message)
                successful += 1
                self.log_message(f"✅ Sent to {group['name']}")

                # Update progress
                self.root.after(0, lambda i=i: self.progress_bar.config(value=i))

                # Rate limiting
                if i < total:
                    await asyncio.sleep(delay)

            except FloodWaitError as e:
                self.log_message(f"⏳ Rate limited, waiting {e.seconds} seconds...")
                await asyncio.sleep(e.seconds)
                # Retry
                try:
                    await self.client.send_message(group['entity'], message)
                    successful += 1
                    self.log_message(f"✅ Sent to {group['name']} (retry)")
                except:
                    failed += 1
                    self.log_message(f"❌ Failed to send to {group['name']} (retry failed)")

            except (PeerFloodError, UserPrivacyRestrictedError):
                failed += 1
                self.log_message(f"❌ Privacy/flood error for {group['name']}")

            except Exception as e:
                failed += 1
                self.log_message(f"❌ Error sending to {group['name']}: {str(e)}")

        # Show results
        success_rate = (successful/(successful+failed)*100) if (successful+failed) > 0 else 0
        result_msg = f"""📊 BULK MESSAGING RESULTS
✅ Successful: {successful}
❌ Failed: {failed}
📈 Success rate: {success_rate:.1f}%"""

        self.log_message(result_msg)
        self.root.after(0, lambda: self.progress_label.config(text="Bulk messaging completed!"))
        self.root.after(0, lambda: messagebox.showinfo("Results", result_msg))

    def run(self):
        """Run the GUI application"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("Application stopped by user")
        except Exception as e:
            self.log_message(f"Unexpected error: {str(e)}")
        finally:
            self.cleanup()

    def on_closing(self):
        """Handle window closing"""
        self.cleanup()
        self.root.destroy()

    def cleanup(self):
        """Cleanup resources"""
        try:
            if self.client and self.is_connected:
                # Disconnect client using the event loop
                future = asyncio.run_coroutine_threadsafe(self.client.disconnect(), self.event_loop)
                future.result(timeout=5)
                self.log_message("Disconnected from Telegram")
        except:
            pass

        try:
            # Stop the event loop
            if self.event_loop and not self.event_loop.is_closed():
                self.event_loop.call_soon_threadsafe(self.event_loop.stop)
        except:
            pass

# Legacy console methods (kept for compatibility but not used in GUI)
class TelegramDMBot:
    def __init__(self):
        pass

    def display_contacts(self, contacts, show_all=True):
        """Display contacts in a nice format"""
        print("\n" + "="*60)
        print("📱 YOUR CONTACTS & CHATS")
        print("="*60)

        users = [c for c in contacts if c['type'] == 'User']
        groups = [c for c in contacts if c['type'] in ['Group', 'Supergroup']]
        channels = [c for c in contacts if c['type'] == 'Channel']
        bots = [c for c in contacts if c['type'] == 'Bot']

        if users and show_all:
            print(f"\n👥 USERS ({len(users)}):")
            if len(users) <= 50:
                for i, contact in enumerate(users, 1):
                    print(f"  {i:2d}. {contact['name']}")
            else:
                for i, contact in enumerate(users[:25], 1):
                    print(f"  {i:2d}. {contact['name']}")
                print(f"  ... and {len(users)-25} more users")

        if groups:
            print(f"\n👨‍👩‍👧‍👦 GROUPS ({len(groups)}):")
            start_idx = len(users) + 1 if show_all else 1
            if len(groups) <= 50:
                for i, contact in enumerate(groups, start_idx):
                    print(f"  {i:2d}. {contact['name']} ({contact['type']})")
            else:
                for i, contact in enumerate(groups[:25], start_idx):
                    print(f"  {i:2d}. {contact['name']} ({contact['type']})")
                print(f"  ... and {len(groups)-25} more groups")

        if channels and show_all:
            print(f"\n📢 CHANNELS ({len(channels)}):")
            start_idx = len(users) + len(groups) + 1
            if len(channels) <= 50:
                for i, contact in enumerate(channels, start_idx):
                    print(f"  {i:2d}. {contact['name']}")
            else:
                for i, contact in enumerate(channels[:25], start_idx):
                    print(f"  {i:2d}. {contact['name']}")
                print(f"  ... and {len(channels)-25} more channels")

        if bots and show_all:
            print(f"\n🤖 BOTS ({len(bots)}):")
            start_idx = len(users) + len(groups) + len(channels) + 1
            if len(bots) <= 50:
                for i, contact in enumerate(bots, start_idx):
                    print(f"  {i:2d}. {contact['name']}")
            else:
                for i, contact in enumerate(bots[:25], start_idx):
                    print(f"  {i:2d}. {contact['name']}")
                print(f"  ... and {len(bots)-25} more bots")

        print("="*60)
    
    def select_recipients(self, contacts):
        """Select recipients for messaging"""
        recipients = []
        
        print("\n📝 SELECT RECIPIENTS:")
        print("Enter numbers separated by commas (e.g., 1,3,5)")
        print("Or enter 'all' to select all contacts")
        print("Or enter 'range' for a range (e.g., 1-10)")
        
        while True:
            selection = input("\nYour selection: ").strip().lower()
            
            if selection == 'all':
                recipients = contacts.copy()
                break
            elif '-' in selection and selection.replace('-', '').isdigit():
                try:
                    start, end = map(int, selection.split('-'))
                    if 1 <= start <= len(contacts) and 1 <= end <= len(contacts):
                        recipients = contacts[start-1:end]
                        break
                    else:
                        print("❌ Range out of bounds!")
                except:
                    print("❌ Invalid range format!")
            else:
                try:
                    indices = [int(x.strip()) for x in selection.split(',')]
                    if all(1 <= i <= len(contacts) for i in indices):
                        recipients = [contacts[i-1] for i in indices]
                        break
                    else:
                        print("❌ Some numbers are out of range!")
                except:
                    print("❌ Invalid selection format!")
        
        print(f"\n✅ Selected {len(recipients)} recipients:")
        for recipient in recipients:
            print(f"  • {recipient['name']} ({recipient['type']})")
        
        return recipients
    
    def compose_message(self):
        """Compose message to send"""
        print("\n✍️  COMPOSE YOUR MESSAGE:")
        print("(Press Enter twice to finish, or type 'cancel' to abort)")
        
        lines = []
        empty_lines = 0
        
        while True:
            line = input()
            if line.lower() == 'cancel':
                return None
            
            if line == '':
                empty_lines += 1
                if empty_lines >= 2:
                    break
            else:
                empty_lines = 0
            
            lines.append(line)
        
        # Remove trailing empty lines
        while lines and lines[-1] == '':
            lines.pop()
        
        message = '\n'.join(lines)
        
        if not message.strip():
            print("❌ Message cannot be empty!")
            return None
        
        print(f"\n📝 Your message ({len(message)} characters):")
        print("-" * 40)
        print(message)
        print("-" * 40)
        
        confirm = input("\nSend this message? (y/n): ").strip().lower()
        return message if confirm == 'y' else None
    
    async def send_messages(self, recipients, message, delay=1):
        """Send messages to selected recipients"""
        print(f"\n🚀 Sending message to {len(recipients)} recipients...")

        successful = 0
        failed = 0
        errors = []

        for i, recipient in enumerate(recipients, 1):
            try:
                print(f"[{i}/{len(recipients)}] Sending to {recipient['name']}...", end=' ')

                await self.client.send_message(recipient['entity'], message)
                print("✅")
                successful += 1

                # Rate limiting - wait between messages
                if i < len(recipients):
                    await asyncio.sleep(delay)

            except FloodWaitError as e:
                print(f"⏳ Rate limited, waiting {e.seconds} seconds...")
                await asyncio.sleep(e.seconds)
                # Retry
                try:
                    await self.client.send_message(recipient['entity'], message)
                    print("✅ (retry successful)")
                    successful += 1
                except Exception as retry_error:
                    print("❌ (retry failed)")
                    failed += 1
                    errors.append(f"{recipient['name']}: {str(retry_error)}")

            except (PeerFloodError, UserPrivacyRestrictedError) as e:
                print("❌ (privacy/flood error)")
                failed += 1
                errors.append(f"{recipient['name']}: {str(e)}")

            except Exception as e:
                print(f"❌ ({str(e)})")
                failed += 1
                errors.append(f"{recipient['name']}: {str(e)}")

        print(f"\n📊 RESULTS:")
        print(f"✅ Successful: {successful}")
        print(f"❌ Failed: {failed}")
        if successful + failed > 0:
            print(f"📈 Success rate: {successful/(successful+failed)*100:.1f}%")

        # Show first few errors if any
        if errors and len(errors) <= 5:
            print(f"\n❌ Errors:")
            for error in errors:
                print(f"  • {error}")
        elif errors:
            print(f"\n❌ First 5 errors (total: {len(errors)}):")
            for error in errors[:5]:
                print(f"  • {error}")

    async def send_to_all_groups(self):
        """Send message to all groups with bulk options"""
        contacts = await self.get_contacts()
        if not contacts:
            print("❌ No contacts found!")
            return

        groups = [c for c in contacts if c['type'] in ['Group', 'Supergroup']]
        if not groups:
            print("❌ No groups found!")
            return

        print(f"\n👨‍👩‍👧‍👦 Found {len(groups)} groups")

        # Show groups with pagination
        self.display_contacts(contacts, show_all=False)

        print(f"\n📤 BULK GROUP MESSAGING OPTIONS:")
        print("1. 🎯 Send to ALL groups")
        print("2. 📝 Select specific groups")
        print("3. 📊 Send to groups by range")
        print("4. 🔍 Search and select groups")
        print("5. 🔙 Back to main menu")

        choice = input("\nSelect option (1-5): ").strip()

        if choice == '1':
            # Send to all groups
            print(f"\n⚠️  You are about to send a message to ALL {len(groups)} groups!")
            confirm = input("Are you sure? Type 'YES' to confirm: ").strip()
            if confirm != 'YES':
                print("❌ Operation cancelled!")
                return

            message = self.compose_message()
            if not message:
                return

            # Ask for delay between messages
            print(f"\n⏱️  RATE LIMITING:")
            print("1. Fast (0.5s delay) - Risk of rate limiting")
            print("2. Normal (1s delay) - Recommended")
            print("3. Safe (2s delay) - Very safe")
            print("4. Custom delay")

            delay_choice = input("Select delay option (1-4): ").strip()
            delay = 1  # default

            if delay_choice == '1':
                delay = 0.5
            elif delay_choice == '2':
                delay = 1
            elif delay_choice == '3':
                delay = 2
            elif delay_choice == '4':
                try:
                    delay = float(input("Enter delay in seconds: "))
                except:
                    delay = 1

            await self.send_messages(groups, message, delay)

        elif choice == '2':
            # Select specific groups
            recipients = self.select_recipients(groups)
            if not recipients:
                return

            message = self.compose_message()
            if not message:
                return

            await self.send_messages(recipients, message)

        elif choice == '3':
            # Send by range
            print(f"\nEnter range (1-{len(groups)}):")
            try:
                start = int(input("Start index: "))
                end = int(input("End index: "))

                if 1 <= start <= len(groups) and 1 <= end <= len(groups) and start <= end:
                    selected_groups = groups[start-1:end]
                    print(f"\n✅ Selected {len(selected_groups)} groups (#{start} to #{end})")

                    message = self.compose_message()
                    if not message:
                        return

                    await self.send_messages(selected_groups, message)
                else:
                    print("❌ Invalid range!")
            except ValueError:
                print("❌ Invalid input!")

        elif choice == '4':
            # Search groups
            search_term = input("\nEnter search term: ").strip().lower()
            if not search_term:
                print("❌ Search term cannot be empty!")
                return

            matching_groups = [g for g in groups if search_term in g['name'].lower()]

            if not matching_groups:
                print(f"❌ No groups found matching '{search_term}'")
                return

            print(f"\n🔍 Found {len(matching_groups)} groups matching '{search_term}':")
            for i, group in enumerate(matching_groups, 1):
                print(f"  {i:2d}. {group['name']} ({group['type']})")

            print(f"\nSend to all {len(matching_groups)} matching groups?")
            confirm = input("Type 'yes' to confirm: ").strip().lower()

            if confirm == 'yes':
                message = self.compose_message()
                if not message:
                    return

                await self.send_messages(matching_groups, message)
            else:
                print("❌ Operation cancelled!")

        elif choice == '5':
            return
        else:
            print("❌ Invalid option!")
    
    async def main_menu(self):
        """Main application menu"""
        while True:
            print("\n" + "="*60)
            print("🚀 TELEGRAM DM BOT - BULK MESSAGING")
            print("="*60)
            print("1. 📤 Send messages to selected contacts")
            print("2. 👨‍👩‍👧‍👦 Bulk send to groups (FAST)")
            print("3. 📊 View contact statistics")
            print("4. 🔄 Refresh contacts")
            print("5. 🚪 Exit")
            print("="*60)

            choice = input("Select option (1-5): ").strip()

            if choice == '1':
                contacts = await self.get_contacts()
                if not contacts:
                    print("❌ No contacts found!")
                    continue

                self.display_contacts(contacts)
                recipients = self.select_recipients(contacts)

                if not recipients:
                    print("❌ No recipients selected!")
                    continue

                message = self.compose_message()
                if not message:
                    print("❌ Message composition cancelled!")
                    continue

                await self.send_messages(recipients, message)

            elif choice == '2':
                await self.send_to_all_groups()

            elif choice == '3':
                contacts = await self.get_contacts()
                if contacts:
                    self.show_statistics(contacts)

            elif choice == '4':
                await self.get_contacts()

            elif choice == '5':
                print("👋 Goodbye!")
                break

            else:
                print("❌ Invalid option!")

    def show_statistics(self, contacts):
        """Show contact statistics"""
        users = [c for c in contacts if c['type'] == 'User']
        groups = [c for c in contacts if c['type'] in ['Group', 'Supergroup']]
        channels = [c for c in contacts if c['type'] == 'Channel']
        bots = [c for c in contacts if c['type'] == 'Bot']

        print("\n" + "="*50)
        print("📊 CONTACT STATISTICS")
        print("="*50)
        print(f"👥 Users: {len(users)}")
        print(f"👨‍👩‍👧‍👦 Groups: {len(groups)}")
        print(f"📢 Channels: {len(channels)}")
        print(f"🤖 Bots: {len(bots)}")
        print(f"📱 Total: {len(contacts)}")
        print("="*50)

        if groups:
            print(f"\n🎯 BULK MESSAGING POTENTIAL:")
            print(f"   • You can send to {len(groups)} groups at once")
            print(f"   • Estimated time (1s delay): {len(groups)} seconds")
            print(f"   • Estimated time (2s delay): {len(groups)*2} seconds")
    
    def show_welcome(self):
        """Show welcome screen"""
        # Clear screen for Windows
        os.system('cls' if os.name == 'nt' else 'clear')

        print("\n" + "="*70)
        print("🚀 TELEGRAM DM BOT - BULK MESSAGING TOOL")
        print("="*70)
        print("📱 Send messages to 1000+ groups efficiently!")
        print("🎯 Perfect for bulk messaging, announcements, and marketing")
        print("⚡ Fast, safe, and user-friendly")
        print("="*70)

        print("\n🔧 READY TO START:")
        print("✅ API credentials configured (PumpX Bot)")
        print("📱 Just login with your Telegram account")
        print("🚀 Start bulk messaging immediately!")

        print("\n📋 WHAT YOU'LL NEED:")
        print("• Your Telegram phone number")
        print("• Access to your phone for verification code")

        print("\n💻 WINDOWS DESKTOP APP VERSION")
        print("📦 Standalone executable - no Python required!")

        print("\n" + "="*70)
        input("Press Enter to login and start...")

    async def run(self):
        """Run the bot"""
        try:
            self.show_welcome()

            if await self.connect():
                try:
                    await self.main_menu()
                finally:
                    if self.client:
                        await self.client.disconnect()
                        print("📱 Disconnected from Telegram")
            else:
                print("❌ Failed to start bot")
        except KeyboardInterrupt:
            print("\n\n👋 Bot stopped by user")
        except Exception as e:
            print(f"\n❌ Unexpected error: {str(e)}")
        finally:
            print("\n🔚 Press Enter to exit...")
            input()

if __name__ == "__main__":
    # Create and run GUI application
    app = TelegramDMBotGUI()
    app.run()
